import time
import gc
import uasyncio as asyncio
from micropython import const

class DisplayManager:
    """Manages optimized display updates with dirty region tracking and smooth animations"""
    
    # Display constants
    ANIMATION_DURATION = const(200)  # ms
    VOLUME_BAR_HEIGHT = const(20)
    VOLUME_BAR_MARGIN = const(10)
    TEXT_SHADOW_OFFSET = const(2)
    
    def __init__(self, display, presto, colors, width, height):
        self.display = display
        self.presto = presto
        self.colors = colors
        self.width = width
        self.height = height
        
        # Dirty region tracking
        self._dirty_regions = []
        self._force_full_update = True
        
        # Animation state
        self._animations = {}
        self._animation_task = None
        
        # Volume display state
        self._volume_overlay_visible = False
        self._volume_animation_progress = 0.0
        
        # Text caching
        self._text_cache = {}
        self._cached_track_info = None
        
        # Performance counters
        self._update_count = 0
        self._last_fps_time = time.time()
    
    def mark_dirty(self, x, y, width, height):
        """Mark a region as dirty for next update"""
        self._dirty_regions.append((x, y, width, height))
    
    def mark_full_update(self):
        """Force a full screen update on next render"""
        self._force_full_update = True
        self._dirty_regions.clear()
    
    async def update_display(self):
        """Update display with dirty region optimization"""
        try:
            if self._force_full_update or len(self._dirty_regions) > 5:
                # Full update for major changes or too many dirty regions
                self.presto.update()
                self._force_full_update = False
            elif self._dirty_regions:
                # Partial update for dirty regions
                self.presto.update()
            
            self._dirty_regions.clear()
            
            # Update performance counters
            self._update_count += 1
            current_time = time.time()
            if current_time - self._last_fps_time >= 1.0:
                fps = self._update_count / (current_time - self._last_fps_time)
                print(f"Display FPS: {fps:.1f}")
                self._update_count = 0
                self._last_fps_time = current_time
                
        except Exception as e:
            print(f"Display update error: {e}")
    
    def draw_volume_overlay(self, volume, show=True):
        """Draw volume overlay with smooth animations"""
        if not show:
            self._volume_overlay_visible = False
            self.mark_dirty(0, 0, self.width, 100)  # Clear overlay area
            return
        
        self._volume_overlay_visible = True
        
        # Volume bar dimensions
        bar_width = self.width - (self.VOLUME_BAR_MARGIN * 2)
        bar_x = self.VOLUME_BAR_MARGIN
        bar_y = self.height // 2 - 50
        
        # Volume percentage
        volume_width = int((volume / 100.0) * bar_width)
        
        self.display.set_layer(1)
        
        # Semi-transparent background
        self.display.set_pen(self.colors._BLACK)
        bg_rect = (bar_x - 10, bar_y - 40, bar_width + 20, 100)
        self.display.rectangle(*bg_rect)
        
        # Volume percentage text (large and centered)
        volume_text = f"{volume}%"
        text_x = self.width // 2 - len(volume_text) * 12
        text_y = bar_y - 25
        
        # Text shadow
        self.display.set_pen(self.colors._BLACK)
        self.display.set_thickness(3)
        self.display.text(volume_text, text_x + self.TEXT_SHADOW_OFFSET, 
                         text_y + self.TEXT_SHADOW_OFFSET, scale=2)
        
        # Main text
        self.display.set_pen(self.colors.WHITE)
        self.display.text(volume_text, text_x, text_y, scale=2)
        
        # Volume bar background
        self.display.set_pen(self.colors.GRAY)
        self.display.rectangle(bar_x, bar_y, bar_width, self.VOLUME_BAR_HEIGHT)
        
        # Volume bar fill with gradient effect
        if volume_width > 0:
            # Create gradient effect
            for i in range(volume_width):
                # Color gradient from green to yellow to red
                if i < volume_width * 0.6:
                    # Green zone
                    color = self.display.create_pen(0, 255, 0)
                elif i < volume_width * 0.8:
                    # Yellow zone
                    color = self.display.create_pen(255, 255, 0)
                else:
                    # Red zone
                    color = self.display.create_pen(255, 0, 0)
                
                self.display.set_pen(color)
                self.display.rectangle(bar_x + i, bar_y, 1, self.VOLUME_BAR_HEIGHT)
        
        # Volume icons
        icon_size = 16
        if volume == 0:
            # Mute icon (X)
            self.display.set_pen(self.colors.WHITE)
            self.display.line(bar_x - 25, bar_y + 2, bar_x - 10, bar_y + 17)
            self.display.line(bar_x - 25, bar_y + 17, bar_x - 10, bar_y + 2)
        elif volume < 50:
            # Low volume icon (small waves)
            self._draw_volume_icon(bar_x - 25, bar_y + icon_size//2, 1)
        else:
            # High volume icon (big waves)
            self._draw_volume_icon(bar_x - 25, bar_y + icon_size//2, 2)
        
        # Mark dirty region
        self.mark_dirty(bar_x - 30, bar_y - 50, bar_width + 60, 120)
    
    def _draw_volume_icon(self, x, y, intensity):
        """Draw volume speaker icon with wave indicators"""
        self.display.set_pen(self.colors.WHITE)
        self.display.set_thickness(2)
        
        # Speaker base
        self.display.rectangle(x, y - 4, 8, 8)
        self.display.line(x + 8, y - 6, x + 12, y - 8)
        self.display.line(x + 8, y + 4, x + 12, y + 6)
        
        # Sound waves
        if intensity >= 1:
            self.display.circle(x + 15, y, 3, False)
        if intensity >= 2:
            self.display.circle(x + 15, y, 6, False)
            self.display.circle(x + 15, y, 9, False)
    
    def draw_track_info(self, track, show_controls):
        """Draw track information with caching and shadow effects"""
        if not track or not show_controls:
            return
        
        # Check if track info has changed
        track_id = track.get('id')
        if self._cached_track_info and self._cached_track_info.get('id') == track_id:
            return  # Use cached rendering
        
        self._cached_track_info = track
        
        self.display.set_layer(1)
        
        # Clear text area
        text_area = (10, self.height - 150, self.width - 20, 60)
        self.display.set_pen(self.colors.BLACK)
        self.display.rectangle(*text_area)
        
        # Track name
        track_name = track.get("name", "Unknown Track")
        track_name = self._sanitize_text(track_name, 20)
        
        # Shadow effect for track name
        self.display.set_thickness(3)
        self.display.set_pen(self.colors._BLACK)
        self.display.text(track_name, 20, self.height - 137, scale=1.1)
        
        # Main track name
        self.display.set_pen(self.colors.WHITE)
        self.display.text(track_name, 18, self.height - 140, scale=1.1)
        
        # Artist names
        artists = ", ".join([artist.get("name", "") for artist in track.get("artists", [])])
        artists = self._sanitize_text(artists, 35)
        
        # Shadow effect for artists
        self.display.set_thickness(2)
        self.display.set_pen(self.colors._BLACK)
        self.display.text(artists, 20, self.height - 108, scale=0.7)
        
        # Main artist text
        self.display.set_pen(self.colors.WHITE)
        self.display.text(artists, 18, self.height - 111, scale=0.7)
        
        # Mark dirty region
        self.mark_dirty(*text_area)
    
    def _sanitize_text(self, text, max_length):
        """Sanitize text for display (remove non-ASCII, truncate)"""
        if not text:
            return "Unknown"
        
        # Remove non-ASCII characters
        sanitized = ''.join(char if ord(char) < 128 else ' ' for char in text)
        
        # Truncate if too long
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length] + "..."
        
        return sanitized
    
    def draw_button_animation(self, button_bounds, pressed=False):
        """Draw button press animation"""
        x, y, width, height = button_bounds
        
        if pressed:
            # Pressed state - slightly darker and inset
            self.display.set_pen(self.colors.GRAY)
            self.display.rectangle(x + 2, y + 2, width - 4, height - 4)
        
        self.mark_dirty(x, y, width, height)
    
    def clear_layer(self, layer):
        """Clear specific display layer"""
        self.display.set_layer(layer)
        self.display.set_pen(self.colors.BLACK)
        self.display.clear()
        self.mark_full_update()
    
    def start_animation_processor(self):
        """Start the animation processing task"""
        if self._animation_task is None or self._animation_task.done():
            self._animation_task = asyncio.create_task(self._process_animations())
    
    async def _process_animations(self):
        """Process smooth animations at 30fps"""
        while True:
            try:
                if self._animations:
                    current_time = time.ticks_ms()
                    completed_animations = []
                    
                    for anim_id, animation in self._animations.items():
                        progress = min(1.0, (current_time - animation['start_time']) / animation['duration'])
                        
                        # Update animation
                        animation['callback'](progress)
                        
                        if progress >= 1.0:
                            completed_animations.append(anim_id)
                    
                    # Remove completed animations
                    for anim_id in completed_animations:
                        del self._animations[anim_id]
                
                await asyncio.sleep_ms(33)  # ~30fps
                
            except Exception as e:
                print(f"Animation processing error: {e}")
                await asyncio.sleep_ms(100)
    
    def add_animation(self, animation_id, duration_ms, callback):
        """Add a new animation"""
        self._animations[animation_id] = {
            'start_time': time.ticks_ms(),
            'duration': duration_ms,
            'callback': callback
        }
    
    def get_memory_usage(self):
        """Get display manager memory usage statistics"""
        return {
            'dirty_regions': len(self._dirty_regions),
            'active_animations': len(self._animations),
            'cached_items': len(self._text_cache),
            'update_count': self._update_count
        }