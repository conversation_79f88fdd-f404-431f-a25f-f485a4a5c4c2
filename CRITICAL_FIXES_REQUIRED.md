# 🚨 CRITICAL FIXES REQUIRED FOR PRESTODECK SPOTIFY APP

## IMMEDIATE ACTION REQUIRED

### 1. MicroPython Compatibility Fix (CRITICAL)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 771-776, 817-830

**Problem**: Code uses `asyncio.to_thread()` and `asyncio.wait_for()` which don't exist in MicroPython.

**Fix**:
```python
# Replace lines 771-784 with:
async def fetch_state_async(self):
    try:
        # Direct async call without threading
        resp = await self.spotify_client.current_playing_async()
        # ... rest of logic
    except Exception as e:
        print(f"Async fetch state error: {e}")
        return None

# Replace lines 817-830 with:
async def get_album_cover_async(self, track):
    try:
        # Use direct urequests call with yield
        await asyncio.sleep_ms(0)  # Yield control
        response = requests.get(resize_url)
        # ... rest of logic
    except Exception as e:
        print(f"Album cover error: {e}")
        return None
```

### 2. Fix Infinite Credentials Loop (CRITICAL)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 225-231

**Problem**: Infinite loop if credentials missing - device becomes unresponsive.

**Fix**:
```python
def get_spotify_client(self):
    if not hasattr(secrets, 'SPOTIFY_CREDENTIALS') or not secrets.SPOTIFY_CREDENTIALS:
        # Show error for 10 seconds then raise exception
        for i in range(5):
            self.clear(1)
            self.display.set_pen(self.colors.WHITE)
            self.display.text("Spotify credentials not found", 40, self.height - 80, scale=.9)
            self.display.text(f"Check secrets.py ({5-i}s)", 60, self.height - 60, scale=.8)
            self.presto.update()
            time.sleep(2)
        raise Exception("Spotify credentials not configured in secrets.py")
    
    session = Session(secrets.SPOTIFY_CREDENTIALS)
    return SpotifyWebApiClient(session)
```

### 3. Fix Touch Event Conflicts (CRITICAL)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 421, 479

**Problem**: Two systems polling same touch interface causes conflicts.

**Fix**: Centralize touch processing in gesture detector:
```python
# In run() method, remove duplicate touch handler:
def run(self):
    loop = asyncio.get_event_loop()
    
    # Start all async components
    loop.create_task(self.initialize_async_components())
    # REMOVE: loop.create_task(self.touch_handler_loop())  # Remove this line
    loop.create_task(self.gesture_detector.process_touch_events())
    loop.create_task(self.display_loop())
    
    # Register button callbacks with gesture detector
    self.gesture_detector.register_button_callbacks(self.buttons)
```

### 4. Add Memory Pressure Handling (HIGH PRIORITY)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 466-472

**Problem**: Image cache doesn't handle memory pressure properly.

**Fix**:
```python
def _cleanup_memory(self):
    try:
        # Check memory pressure first
        if hasattr(gc, 'mem_free'):
            free_mem = gc.mem_free()
            if free_mem < 50000:  # Less than 50KB free
                # Emergency cleanup - clear all cache
                self._image_cache.clear()
                print(f"Emergency: Cleared all cached images, free memory: {free_mem}")
            elif free_mem < 100000:  # Less than 100KB free
                # Aggressive cleanup - keep only 1 image
                if len(self._image_cache) > 1:
                    keys = list(self._image_cache.keys())
                    for key in keys[:-1]:
                        del self._image_cache[key]
                    print(f"Aggressive cleanup: Kept 1 cached image, free memory: {free_mem}")
        
        gc.collect()
    except Exception as e:
        print(f"Memory cleanup error: {e}")
```

### 5. Remove Debug Logging from Production (HIGH PRIORITY)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 576-720

**Problem**: Extensive debug logging in main loop causes performance issues.

**Fix**: Add debug flag:
```python
class Spotify(BaseApp):
    def __init__(self):
        super().__init__(ambient_light=True, full_res=True, layers=2)
        
        # Add debug flag
        self.debug_mode = False  # Set to True only for debugging
        
    def debug_print(self, message):
        if self.debug_mode:
            print(message)
    
    # Replace all print() calls in display_loop with self.debug_print()
```

### 6. Add Token Refresh Logic (HIGH PRIORITY)
**File**: `src/applications/spotify/spotify_client.py`

**Problem**: No token refresh - app stops working after 1 hour.

**Fix**: Add token refresh method:
```python
async def refresh_token_if_needed(self):
    """Check and refresh token if needed"""
    try:
        # Check if token is expired (implement based on your token structure)
        if self.session.is_token_expired():
            await self.session.refresh_access_token()
            print("Token refreshed successfully")
    except Exception as e:
        print(f"Token refresh failed: {e}")
```

## TESTING RECOMMENDATIONS

1. **Memory Testing**: Monitor memory usage during extended operation
2. **Network Testing**: Test with poor network conditions
3. **Touch Testing**: Verify all touch interactions work correctly
4. **Error Testing**: Test with missing credentials, network failures
5. **Performance Testing**: Measure frame rates and response times

## DEPLOYMENT CHECKLIST

- [ ] Fix MicroPython compatibility issues
- [ ] Fix infinite credentials loop
- [ ] Centralize touch event processing
- [ ] Implement memory pressure handling
- [ ] Remove/conditional debug logging
- [ ] Add token refresh logic
- [ ] Test on actual ESP32 hardware
- [ ] Verify memory usage under load
- [ ] Test error recovery scenarios
