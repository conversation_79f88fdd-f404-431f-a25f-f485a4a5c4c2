import time
import uasyncio as asyncio
from micropython import const

class GestureDetector:
    """Detects touch gestures including swipes, taps, and long presses with debouncing"""
    
    # Gesture constants
    TAP_MAX_DURATION = const(300)     # ms
    LONG_PRESS_DURATION = const(800)  # ms
    SWIPE_MIN_DISTANCE = const(50)    # pixels
    DEBOUNCE_TIME = const(50)         # ms
    
    # Gesture types
    GESTURE_TAP = const(1)
    GESTURE_LONG_PRESS = const(2)
    GESTURE_SWIPE_UP = const(3)
    GESTURE_SWIPE_DOWN = const(4)
    GESTURE_SWIPE_LEFT = const(5)
    GESTURE_SWIPE_RIGHT = const(6)
    GESTURE_VOLUME_UP = const(7)
    GESTURE_VOLUME_DOWN = const(8)
    
    def __init__(self, touch, width, height):
        self.touch = touch
        self.width = width
        self.height = height
        
        # Touch state tracking
        self._touch_start_time = 0
        self._touch_start_pos = None
        self._last_touch_time = 0
        self._is_touching = False
        self._gesture_detected = False
        
        # Volume gesture zones (right side of screen)
        self._volume_zone_x = width * 0.8  # Right 20% of screen
        self._volume_sensitivity = height / 100  # 1% of screen height per volume unit
        
        # Callbacks
        self._gesture_callbacks = {}
    
    def register_gesture_callback(self, gesture_type, callback):
        """Register callback for specific gesture type"""
        if gesture_type not in self._gesture_callbacks:
            self._gesture_callbacks[gesture_type] = []
        self._gesture_callbacks[gesture_type].append(callback)
    
    async def process_touch_events(self):
        """Main touch processing loop with gesture detection"""
        while True:
            try:
                self.touch.poll()
                current_time = time.ticks_ms()
                
                if self.touch.state:
                    await self._handle_touch_down(current_time)
                else:
                    await self._handle_touch_up(current_time)
                
                await asyncio.sleep_ms(10)  # 100Hz polling rate
                
            except Exception as e:
                print(f"Touch processing error: {e}")
                await asyncio.sleep_ms(50)
    
    async def _handle_touch_down(self, current_time):
        """Handle touch down events"""
        # Debouncing
        if current_time - self._last_touch_time < self.DEBOUNCE_TIME:
            return
        
        if not self._is_touching:
            # Start of new touch
            self._is_touching = True
            self._touch_start_time = current_time
            self._touch_start_pos = (self.touch.x, self.touch.y)
            self._gesture_detected = False
            self._last_touch_time = current_time
        
        # Check for long press
        touch_duration = current_time - self._touch_start_time
        if touch_duration >= self.LONG_PRESS_DURATION and not self._gesture_detected:
            await self._trigger_gesture(self.GESTURE_LONG_PRESS, self._touch_start_pos)
    
    async def _handle_touch_up(self, current_time):
        """Handle touch up events"""
        if not self._is_touching:
            return
        
        self._is_touching = False
        touch_duration = current_time - self._touch_start_time
        end_pos = (self.touch.x, self.touch.y)
        
        if self._gesture_detected:
            return  # Gesture already processed
        
        # Calculate movement
        if self._touch_start_pos:
            dx = end_pos[0] - self._touch_start_pos[0]
            dy = end_pos[1] - self._touch_start_pos[1]
            distance = (dx * dx + dy * dy) ** 0.5
            
            # Check for volume gestures first (in volume zone)
            if self._touch_start_pos[0] >= self._volume_zone_x:
                await self._detect_volume_gesture(dy)
            # Check for swipe gestures
            elif distance >= self.SWIPE_MIN_DISTANCE:
                await self._detect_swipe_gesture(dx, dy)
            # Check for tap
            elif touch_duration <= self.TAP_MAX_DURATION:
                await self._trigger_gesture(self.GESTURE_TAP, self._touch_start_pos)
    
    async def _detect_volume_gesture(self, dy):
        """Detect volume up/down gestures in volume zone"""
        volume_change = -dy / self._volume_sensitivity  # Invert Y axis
        
        if abs(volume_change) >= 5:  # Minimum 5% volume change
            if volume_change > 0:
                await self._trigger_gesture(self.GESTURE_VOLUME_UP, None, {"volume_delta": volume_change})
            else:
                await self._trigger_gesture(self.GESTURE_VOLUME_DOWN, None, {"volume_delta": volume_change})
    
    async def _detect_swipe_gesture(self, dx, dy):
        """Detect directional swipe gestures"""
        if abs(dx) > abs(dy):
            # Horizontal swipe
            if dx > 0:
                await self._trigger_gesture(self.GESTURE_SWIPE_RIGHT, self._touch_start_pos)
            else:
                await self._trigger_gesture(self.GESTURE_SWIPE_LEFT, self._touch_start_pos)
        else:
            # Vertical swipe
            if dy > 0:
                await self._trigger_gesture(self.GESTURE_SWIPE_DOWN, self._touch_start_pos)
            else:
                await self._trigger_gesture(self.GESTURE_SWIPE_UP, self._touch_start_pos)
    
    async def _trigger_gesture(self, gesture_type, position, data=None):
        """Trigger gesture callbacks"""
        self._gesture_detected = True
        
        if gesture_type in self._gesture_callbacks:
            for callback in self._gesture_callbacks[gesture_type]:
                try:
                    if data:
                        await callback(position, **data)
                    else:
                        await callback(position)
                except Exception as e:
                    print(f"Gesture callback error: {e}")
    
    def is_in_volume_zone(self, x, y):
        """Check if coordinates are in volume control zone"""
        return x >= self._volume_zone_x
    
    def get_volume_zone_bounds(self):
        """Get volume zone boundaries for UI rendering"""
        return (int(self._volume_zone_x), 0, self.width - int(self._volume_zone_x), self.height)

class ButtonDebouncer:
    """Debounces button presses to prevent multiple rapid activations"""
    
    def __init__(self, debounce_time_ms=50):
        self.debounce_time = debounce_time_ms
        self._last_press_times = {}
    
    def is_debounced(self, button_name):
        """Check if button press should be processed (not debounced)"""
        current_time = time.ticks_ms()
        last_time = self._last_press_times.get(button_name, 0)
        
        if time.ticks_diff(current_time, last_time) >= self.debounce_time:
            self._last_press_times[button_name] = current_time
            return True
        
        return False
    
    def reset_button(self, button_name):
        """Reset debounce timer for a specific button"""
        self._last_press_times[button_name] = 0