"""
Comprehensive test suite for MicroPython compatibility on ESP32 PrestoDeck hardware.
Tests queue functionality, memory management, network operations, and performance.
"""

import gc
import time
import uasyncio as asyncio
from micropython import const

# Import modules to test
try:
    from .micropython_queue import Queue, QueueEmpty, QueueFull, QueueClosed, test_queue_performance
    from .spotify_client import SpotifyWebApiClient, Session
    from .memory_manager import memory_manager
    print("DEBUG: ✓ All test modules imported successfully")
except ImportError as e:
    print(f"DEBUG: ✗ Test module import failed: {e}")
    raise

# Test configuration constants
_TEST_TIMEOUT = const(30)  # 30 second timeout for tests
_MEMORY_PRESSURE_THRESHOLD = const(25600)  # 25KB
_MAX_TEST_QUEUE_SIZE = const(15)  # Reduced for 520KB RAM

class TestResult:
    """Test result container"""
    def __init__(self, name):
        self.name = name
        self.passed = False
        self.error = None
        self.duration = 0
        self.memory_usage = {}
        self.details = {}

class MicroPythonCompatibilityTester:
    """Comprehensive tester for MicroPython compatibility on ESP32"""
    
    def __init__(self):
        self.results = []
        self.start_memory = 0
        self.total_tests = 0
        self.passed_tests = 0
        
    async def run_all_tests(self):
        """Run complete test suite"""
        print("=" * 60)
        print("MICROPYTHON COMPATIBILITY TEST SUITE")
        print("ESP32 PrestoDeck Hardware - 520KB RAM")
        print("=" * 60)
        
        # Record initial memory
        self._record_initial_memory()
        
        # Core functionality tests
        await self._test_queue_basic_operations()
        await self._test_queue_memory_management()
        await self._test_queue_async_operations()
        await self._test_queue_edge_cases()
        
        # Memory management tests
        await self._test_memory_pressure()
        await self._test_garbage_collection()
        
        # Performance tests
        await self._test_performance_benchmarks()
        
        # Network and API tests (if credentials available)
        await self._test_network_operations()
        
        # ESP32 specific tests
        await self._test_dual_core_safety()
        await self._test_interrupt_handling()
        
        # Cleanup tests
        await self._test_cleanup_procedures()
        
        # Generate final report
        self._generate_report()
        
        return self.passed_tests == self.total_tests
    
    def _record_initial_memory(self):
        """Record initial memory state"""
        try:
            gc.collect()
            if hasattr(gc, 'mem_free'):
                self.start_memory = gc.mem_free()
                print(f"Initial free memory: {self.start_memory} bytes")
        except:
            self.start_memory = 0
    
    async def _test_queue_basic_operations(self):
        """Test basic queue operations"""
        result = TestResult("Queue Basic Operations")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting basic queue operations...")
            queue = Queue(maxsize=10)
            
            # Test put operations
            for i in range(5):
                await queue.put(f"item_{i}")
            
            assert queue.qsize() == 5, f"Expected size 5, got {queue.qsize()}"
            assert not queue.empty(), "Queue should not be empty"
            
            # Test get operations
            for i in range(5):
                item = await queue.get()
                assert item == f"item_{i}", f"Expected item_{i}, got {item}"
            
            assert queue.empty(), "Queue should be empty"
            assert queue.qsize() == 0, f"Expected size 0, got {queue.qsize()}"
            
            # Test nowait operations
            queue.put_nowait("nowait_item")
            item = queue.get_nowait()
            assert item == "nowait_item", f"Expected nowait_item, got {item}"
            
            await queue.close()
            result.passed = True
            print("✓ Basic queue operations passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Basic queue operations failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_queue_memory_management(self):
        """Test queue memory management under pressure"""
        result = TestResult("Queue Memory Management")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting queue memory management...")
            queue = Queue(maxsize=_MAX_TEST_QUEUE_SIZE)
            
            # Fill queue with larger objects
            large_items = []
            for i in range(_MAX_TEST_QUEUE_SIZE):
                item = {
                    'id': i,
                    'data': 'x' * 100,  # 100 char string
                    'nested': {'value': i * 2}
                }
                large_items.append(item)
                await queue.put(item)
            
            # Check memory usage
            memory_before = self._get_free_memory()
            
            # Empty the queue
            retrieved_items = []
            for i in range(_MAX_TEST_QUEUE_SIZE):
                item = await queue.get()
                retrieved_items.append(item)
            
            # Force garbage collection
            gc.collect()
            memory_after = self._get_free_memory()
            
            # Verify memory was reclaimed
            memory_reclaimed = memory_after - memory_before
            print(f"Memory reclaimed: {memory_reclaimed} bytes")
            
            # Verify data integrity
            for i, item in enumerate(retrieved_items):
                assert item['id'] == i, f"Data corruption at index {i}"
            
            await queue.close()
            result.passed = True
            result.details['memory_reclaimed'] = memory_reclaimed
            print("✓ Memory management test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Memory management test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_queue_async_operations(self):
        """Test concurrent async operations"""
        result = TestResult("Queue Async Operations")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting async queue operations...")
            queue = Queue(maxsize=10)
            
            # Producer task
            async def producer(queue, items):
                for item in items:
                    await queue.put(item)
                    await asyncio.sleep_ms(1)  # Yield control
            
            # Consumer task
            async def consumer(queue, expected_count):
                consumed = []
                for _ in range(expected_count):
                    item = await queue.get()
                    consumed.append(item)
                    await asyncio.sleep_ms(1)  # Yield control
                return consumed
            
            # Run producer and consumer concurrently
            items_to_produce = [f"async_item_{i}" for i in range(8)]
            
            # Start both tasks
            producer_task = asyncio.create_task(producer(queue, items_to_produce))
            consumer_task = asyncio.create_task(consumer(queue, len(items_to_produce)))
            
            # Wait for completion
            await producer_task
            consumed_items = await consumer_task
            
            # Verify results
            assert len(consumed_items) == len(items_to_produce), "Item count mismatch"
            assert set(consumed_items) == set(items_to_produce), "Item content mismatch"
            
            await queue.close()
            result.passed = True
            print("✓ Async operations test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Async operations test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_queue_edge_cases(self):
        """Test queue edge cases and error conditions"""
        result = TestResult("Queue Edge Cases")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting queue edge cases...")
            queue = Queue(maxsize=3)
            
            # Test queue full condition
            for i in range(3):
                queue.put_nowait(f"item_{i}")
            
            assert queue.full(), "Queue should be full"
            
            try:
                queue.put_nowait("overflow_item")
                assert False, "Should have raised QueueFull"
            except QueueFull:
                pass  # Expected
            
            # Test queue empty condition
            for i in range(3):
                queue.get_nowait()
            
            assert queue.empty(), "Queue should be empty"
            
            try:
                queue.get_nowait()
                assert False, "Should have raised QueueEmpty"
            except QueueEmpty:
                pass  # Expected
            
            # Test queue closure
            await queue.close()
            
            try:
                await queue.put("closed_item")
                assert False, "Should have raised QueueClosed"
            except QueueClosed:
                pass  # Expected
            
            result.passed = True
            print("✓ Edge cases test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Edge cases test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_memory_pressure(self):
        """Test behavior under memory pressure"""
        result = TestResult("Memory Pressure Test")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting memory pressure handling...")
            
            # Create memory pressure
            memory_hogs = []
            initial_memory = self._get_free_memory()
            
            # Allocate memory until we're under pressure
            while self._get_free_memory() > _MEMORY_PRESSURE_THRESHOLD:
                memory_hogs.append('x' * 1024)  # 1KB chunks
                if len(memory_hogs) > 100:  # Safety limit
                    break
            
            pressure_memory = self._get_free_memory()
            print(f"Created memory pressure: {pressure_memory} bytes free")
            
            # Test queue operations under pressure
            queue = Queue(maxsize=5)
            
            for i in range(3):
                await queue.put(f"pressure_item_{i}")
            
            for i in range(3):
                item = await queue.get()
                assert item == f"pressure_item_{i}", "Data corruption under pressure"
            
            await queue.close()
            
            # Clean up memory hogs
            del memory_hogs
            gc.collect()
            
            final_memory = self._get_free_memory()
            print(f"Memory after cleanup: {final_memory} bytes")
            
            result.passed = True
            result.details = {
                'initial_memory': initial_memory,
                'pressure_memory': pressure_memory,
                'final_memory': final_memory
            }
            print("✓ Memory pressure test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Memory pressure test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_garbage_collection(self):
        """Test garbage collection behavior"""
        result = TestResult("Garbage Collection")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting garbage collection...")
            
            initial_memory = self._get_free_memory()
            
            # Create and destroy objects
            for cycle in range(3):
                objects = []
                for i in range(20):
                    obj = {
                        'data': f"gc_test_{cycle}_{i}",
                        'refs': [j for j in range(10)]
                    }
                    objects.append(obj)
                
                # Clear references
                del objects
                gc.collect()
                
                cycle_memory = self._get_free_memory()
                print(f"Cycle {cycle} memory: {cycle_memory} bytes")
            
            final_memory = self._get_free_memory()
            memory_recovered = final_memory - initial_memory
            
            result.passed = True
            result.details = {
                'initial_memory': initial_memory,
                'final_memory': final_memory,
                'memory_recovered': memory_recovered
            }
            print(f"✓ GC test passed, recovered {memory_recovered} bytes")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ GC test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_performance_benchmarks(self):
        """Run performance benchmarks"""
        result = TestResult("Performance Benchmarks")
        start_time = time.ticks_ms()
        
        try:
            print("\nRunning performance benchmarks...")
            
            # Queue performance test
            queue_duration = await test_queue_performance()
            
            # Memory manager test
            if hasattr(memory_manager, 'get_memory_info'):
                memory_info = memory_manager.get_memory_info()
            else:
                memory_info = {}
            
            result.passed = True
            result.details = {
                'queue_performance_ms': queue_duration,
                'memory_info': memory_info
            }
            print(f"✓ Performance benchmarks completed in {queue_duration}ms")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Performance benchmarks failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_network_operations(self):
        """Test network operations (mock/simulated)"""
        result = TestResult("Network Operations")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting network operations...")
            
            # Mock session for testing
            class MockSession:
                def __init__(self):
                    self.device_id = "test_device"
                    self.credentials = {'access_token': 'test_token'}
                
                def get(self, url, **kwargs):
                    return {'test': 'response'}
                
                def put(self, url, json=None, **kwargs):
                    return {'status': 'ok'}
            
            # Test spotify client initialization
            session = MockSession()
            client = SpotifyWebApiClient(session)
            
            # Test request queuing
            async def mock_request():
                await asyncio.sleep_ms(10)
                return {'mock': 'result'}
            
            await client._queue_request(mock_request)
            
            # Start processor
            await client.start_request_processor()
            await asyncio.sleep_ms(100)  # Let it process
            
            # Cleanup
            await client.cleanup()
            
            result.passed = True
            print("✓ Network operations test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Network operations test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_dual_core_safety(self):
        """Test ESP32 dual-core thread safety"""
        result = TestResult("Dual-Core Safety")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting dual-core safety...")
            
            queue = Queue(maxsize=10)
            
            # Simulate concurrent access from multiple "cores"
            async def core_task(core_id, queue, operations):
                for i in range(operations):
                    await queue.put(f"core_{core_id}_item_{i}")
                    await asyncio.sleep_ms(1)
                    item = await queue.get()
                    await asyncio.sleep_ms(1)
            
            # Run multiple tasks concurrently
            tasks = []
            for core_id in range(2):
                task = asyncio.create_task(core_task(core_id, queue, 5))
                tasks.append(task)
            
            # Wait for all tasks
            for task in tasks:
                await task
            
            await queue.close()
            
            result.passed = True
            print("✓ Dual-core safety test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Dual-core safety test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_interrupt_handling(self):
        """Test interrupt and exception handling"""
        result = TestResult("Interrupt Handling")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting interrupt handling...")
            
            queue = Queue(maxsize=5)
            
            # Test graceful handling of interrupts
            async def interruptible_task():
                try:
                    for i in range(100):  # Long running task
                        await queue.put(f"interrupt_item_{i}")
                        if i == 3:  # Simulate interrupt
                            raise KeyboardInterrupt("Simulated interrupt")
                        await asyncio.sleep_ms(1)
                except KeyboardInterrupt:
                    print("Gracefully handling interrupt...")
                    await queue.close()
                    raise
            
            try:
                await interruptible_task()
            except KeyboardInterrupt:
                pass  # Expected
            
            result.passed = True
            print("✓ Interrupt handling test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Interrupt handling test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    async def _test_cleanup_procedures(self):
        """Test cleanup procedures"""
        result = TestResult("Cleanup Procedures")
        start_time = time.ticks_ms()
        
        try:
            print("\nTesting cleanup procedures...")
            
            # Create resources that need cleanup
            queues = []
            for i in range(3):
                queue = Queue(maxsize=5)
                for j in range(3):
                    await queue.put(f"cleanup_item_{i}_{j}")
                queues.append(queue)
            
            # Cleanup all resources
            for queue in queues:
                await queue.close()
            
            # Force garbage collection
            gc.collect()
            
            result.passed = True
            print("✓ Cleanup procedures test passed")
            
        except Exception as e:
            result.error = str(e)
            print(f"✗ Cleanup procedures test failed: {e}")
        
        result.duration = time.ticks_diff(time.ticks_ms(), start_time)
        result.memory_usage = self._get_memory_info()
        self._add_result(result)
    
    def _get_free_memory(self):
        """Get current free memory"""
        try:
            gc.collect()
            return gc.mem_free() if hasattr(gc, 'mem_free') else 0
        except:
            return 0
    
    def _get_memory_info(self):
        """Get comprehensive memory information"""
        try:
            return {
                'free': gc.mem_free() if hasattr(gc, 'mem_free') else 0,
                'allocated': gc.mem_alloc() if hasattr(gc, 'mem_alloc') else 0
            }
        except:
            return {'free': 0, 'allocated': 0}
    
    def _add_result(self, result):
        """Add test result"""
        self.results.append(result)
        self.total_tests += 1
        if result.passed:
            self.passed_tests += 1
    
    def _generate_report(self):
        """Generate final test report"""
        print("\n" + "=" * 60)
        print("FINAL TEST REPORT")
        print("=" * 60)
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {(self.passed_tests / self.total_tests * 100):.1f}%")
        
        print("\nDETAILED RESULTS:")
        print("-" * 60)
        
        for result in self.results:
            status = "✓ PASS" if result.passed else "✗ FAIL"
            print(f"{result.name:<30} {status:<8} {result.duration:>6}ms")
            
            if result.error:
                print(f"  Error: {result.error}")
            
            if result.details:
                for key, value in result.details.items():
                    print(f"  {key}: {value}")
        
        print("\nMEMORY SUMMARY:")
        print("-" * 60)
        final_memory = self._get_free_memory()
        memory_used = self.start_memory - final_memory
        print(f"Initial Memory: {self.start_memory} bytes")
        print(f"Final Memory:   {final_memory} bytes")
        print(f"Memory Used:    {memory_used} bytes")
        
        print("=" * 60)

# Main test runner
async def run_compatibility_tests():
    """Run the complete compatibility test suite"""
    tester = MicroPythonCompatibilityTester()
    success = await tester.run_all_tests()
    return success

if __name__ == "__main__":
    # Run tests
    asyncio.run(run_compatibility_tests())