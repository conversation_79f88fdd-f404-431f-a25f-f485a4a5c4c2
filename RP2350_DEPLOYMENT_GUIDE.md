# 🚀 RP2350 DEPLOYMENT GUIDE - PRESTODECK SPOTIFY APP

## PRE-DEPLOYMENT CHECKLIST

### ✅ **CRITICAL FIXES IMPLEMENTED**
- [x] Fixed infinite credentials loop (graceful timeout)
- [x] Implemented touch synchronization (asyncio.Lock)
- [x] Optimized memory management for 520KB SRAM
- [x] Added conditional debug logging (performance improvement)
- [x] Created comprehensive test suite

### ✅ **CONFIGURATION REQUIREMENTS**

#### 1. **WiFi Configuration**
Edit `src/secrets.py`:
```python
WIFI_SSID = "YourWiFiNetwork"
WIFI_PASSWORD = "YourWiFiPassword"
```

#### 2. **Spotify API Configuration**
Add your Spotify credentials to `src/secrets.py`:
```python
SPOTIFY_CREDENTIALS = {
    'access_token': 'your_access_token',
    'refresh_token': 'your_refresh_token', 
    'client_id': 'your_client_id',
    'client_secret': 'your_client_secret',
    'device_id': 'your_device_id'  # Optional
}
```

## DEPLOYMENT STEPS

### **STEP 1: Hardware Preparation**
1. **Connect Pimoroni Presto** to computer via USB-C
2. **Install Thonny IDE** (if not already installed)
3. **Set interpreter** to "MicroPython (Raspberry Pi Pico)"
4. **Verify connection** in Thonny

### **STEP 2: Pre-Deployment Testing**
Run compatibility tests on the RP2350:

```python
# Test 1: Basic RP2350 compatibility
exec(open('src/applications/spotify/test_rp2350_compatibility.py').read())

# Test 2: Spotify-specific asyncio patterns  
exec(open('src/applications/spotify/test_spotify_asyncio.py').read())

# Test 3: Full deployment readiness
exec(open('src/applications/spotify/test_deployment.py').read())
```

**Expected Results:**
- ✅ All critical tests should pass
- ✅ Success rate should be >90%
- ✅ No critical failures

### **STEP 3: File Upload**
Upload all files to RP2350:

```
src/
├── main.py
├── base.py  
├── secrets.py (configured)
└── applications/
    └── spotify/
        ├── spotify.py (fixed)
        ├── spotify_client.py
        ├── volume_controller.py
        ├── gesture_detector.py (updated)
        ├── display_manager.py
        ├── memory_manager.py
        ├── micropython_queue.py
        ├── icon.png
        └── icons/
            ├── play.png
            ├── pause.png
            ├── next.png
            ├── previous.png
            ├── shuffle_on.png
            ├── shuffle_off.png
            ├── repeat_on.png
            ├── repeat_off.png
            ├── light_on.png
            ├── light_off.png
            └── exit.png
```

### **STEP 4: Initial Deployment**
1. **Upload files** using Thonny's file manager
2. **Configure secrets.py** with your credentials
3. **Run main.py** to test basic functionality

### **STEP 5: Performance Optimization**
For production deployment, optimize settings in `spotify.py`:

```python
class Spotify(BaseApp):
    def __init__(self):
        super().__init__(ambient_light=True, full_res=True, layers=2)
        
        # Production settings
        self.debug_mode = False          # Disable debug logging
        self.debug_performance = False   # Disable performance monitoring
```

## TROUBLESHOOTING GUIDE

### **Common Issues and Solutions**

#### 🔴 **"Spotify credentials not found"**
**Cause**: Missing or incorrect credentials in secrets.py
**Solution**: 
1. Verify `SPOTIFY_CREDENTIALS` is properly configured
2. Check for syntax errors in secrets.py
3. Ensure credentials are valid and not expired

#### 🔴 **WiFi Connection Failed**
**Cause**: Incorrect WiFi credentials or network issues
**Solution**:
1. Verify SSID and password in secrets.py
2. Check WiFi network is 2.4GHz (RP2350 limitation)
3. Ensure network allows new device connections

#### 🔴 **Memory Errors**
**Cause**: Insufficient memory for album covers
**Solution**:
1. Reduce `_cache_size_limit` in spotify.py
2. Enable aggressive memory cleanup
3. Check for memory leaks in custom code

#### 🔴 **Touch Not Responding**
**Cause**: Touch event conflicts or hardware issues
**Solution**:
1. Verify touch synchronization is working
2. Check for hardware connection issues
3. Restart application

#### 🔴 **API Timeouts**
**Cause**: Network issues or API rate limiting
**Solution**:
1. Check internet connection stability
2. Verify Spotify API credentials
3. Reduce API call frequency

### **Performance Monitoring**

Enable performance monitoring for debugging:
```python
# In spotify.py __init__ method:
self.debug_mode = True           # Enable debug logging
self.debug_performance = True    # Enable performance monitoring
```

**Monitor these metrics:**
- **Display FPS**: Should be >10 FPS
- **Memory Usage**: Should stay <90% of 520KB
- **API Response Time**: Should be <2 seconds
- **Touch Response**: Should be <100ms

### **Memory Usage Guidelines**

**RP2350 Memory Allocation:**
- **System overhead**: ~100KB
- **Application code**: ~150KB  
- **Album cache (4 images)**: ~400KB
- **Available for operations**: ~70KB

**Memory Thresholds:**
- **Conservative**: 300KB used (58% of SRAM)
- **Aggressive**: 400KB used (77% of SRAM)  
- **Emergency**: 450KB used (87% of SRAM)

## PRODUCTION DEPLOYMENT

### **Final Checklist**
- [ ] All tests pass with >90% success rate
- [ ] WiFi and Spotify credentials configured
- [ ] Debug logging disabled for performance
- [ ] Memory usage optimized
- [ ] Touch responsiveness verified
- [ ] API functionality tested
- [ ] Error recovery tested

### **Deployment Verification**
After deployment, verify:

1. **Startup**: App starts without errors
2. **WiFi**: Connects to network successfully  
3. **Spotify**: Displays current track
4. **Touch**: All buttons respond correctly
5. **Gestures**: Swipe and tap gestures work
6. **Volume**: Volume control functions
7. **Memory**: No memory errors during operation
8. **Performance**: Smooth animations and responses

### **Monitoring and Maintenance**

**Regular Checks:**
- Monitor memory usage trends
- Check for API rate limiting
- Verify token refresh functionality
- Test error recovery scenarios

**Updates:**
- Keep Spotify API credentials current
- Monitor for MicroPython updates
- Update application as needed

## EXPECTED PERFORMANCE ON RP2350

### **Optimal Performance Targets**
- **Display FPS**: 15-20 FPS (smooth animations)
- **Touch Response**: <50ms (excellent responsiveness)
- **Memory Usage**: 60-80% of 520KB SRAM
- **API Response**: 200-500ms (network dependent)
- **Battery Life**: Excellent (efficient ARM Cortex-M33)

### **Acceptable Performance Ranges**
- **Display FPS**: 10-15 FPS (good responsiveness)
- **Touch Response**: 50-100ms (acceptable)
- **Memory Usage**: 80-90% of 520KB SRAM
- **API Response**: 500ms-1s (slower network)

## SUCCESS INDICATORS

### **🟢 Deployment Successful**
- All critical tests pass
- App starts and runs without errors
- Touch and gestures work correctly
- Spotify integration functional
- Memory usage stable
- Performance meets targets

### **🟡 Deployment with Issues**
- Minor test failures
- Occasional errors but recovers
- Some performance degradation
- Memory usage high but stable

### **🔴 Deployment Failed**
- Critical test failures
- App crashes or hangs
- Touch not working
- Cannot connect to Spotify
- Memory errors
- Severe performance issues

## SUPPORT AND DEBUGGING

For deployment issues:
1. Run diagnostic tests first
2. Check hardware connections
3. Verify configuration files
4. Monitor memory and performance
5. Check network connectivity
6. Review error logs

The RP2350 platform provides excellent compatibility for the PrestoDeck Spotify application with proper configuration and the implemented fixes.
