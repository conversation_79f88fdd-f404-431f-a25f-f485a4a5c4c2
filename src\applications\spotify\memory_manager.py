import gc
import time
import uasyncio as asyncio

# Debug: Check micropython imports availability
print("DEBUG: Checking micropython module imports...")
try:
    from micropython import const
    print("DEBUG: ✓ micropython.const imported successfully")
except ImportError as e:
    print(f"DEBUG: ✗ micropython.const import failed: {e}")
    # Fallback const function
    def const(x):
        return x

try:
    from micropython import mem_info
    print("DEBUG: ✓ micropython.mem_info imported successfully")
except ImportError as e:
    print(f"DEBUG: ✗ micropython.mem_info import failed: {e}")
    # Fallback mem_info function
    def mem_info():
        print("mem_info() not available in this MicroPython version")

class MemoryManager:
    """Manages memory usage, garbage collection, and resource cleanup"""
    
    # Memory thresholds
    LOW_MEMORY_THRESHOLD = const(20480)  # 20KB
    CRITICAL_MEMORY_THRESHOLD = const(10240)  # 10KB
    GC_INTERVAL = const(30)  # seconds
    
    def __init__(self):
        self._last_gc = 0
        self._memory_stats = {}
        self._cleanup_callbacks = []
        self._monitoring = False
        
    def register_cleanup_callback(self, callback):
        """Register callback for memory cleanup"""
        self._cleanup_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start memory monitoring task"""
        if self._monitoring:
            return
        
        self._monitoring = True
        asyncio.create_task(self._monitor_memory())
    
    def stop_monitoring(self):
        """Stop memory monitoring"""
        self._monitoring = False
    
    async def _monitor_memory(self):
        """Monitor memory usage and trigger cleanup when needed"""
        while self._monitoring:
            try:
                current_time = time.time()
                
                # Check if it's time for regular GC
                if current_time - self._last_gc > self.GC_INTERVAL:
                    await self.collect_garbage()
                
                # Check memory levels
                free_memory = self.get_free_memory()
                if free_memory < self.CRITICAL_MEMORY_THRESHOLD:
                    print(f"CRITICAL: Low memory {free_memory} bytes")
                    await self.emergency_cleanup()
                elif free_memory < self.LOW_MEMORY_THRESHOLD:
                    print(f"WARNING: Low memory {free_memory} bytes")
                    await self.collect_garbage()
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                print(f"Memory monitoring error: {e}")
                await asyncio.sleep(10)
    
    async def collect_garbage(self):
        """Perform garbage collection with timing"""
        start_time = time.ticks_ms()
        
        # Force garbage collection
        gc.collect()
        
        end_time = time.ticks_ms()
        gc_time = time.ticks_diff(end_time, start_time)
        
        self._last_gc = time.time()
        
        # Update stats
        self._memory_stats.update({
            'last_gc_time': gc_time,
            'free_memory': self.get_free_memory(),
            'allocated_memory': self.get_allocated_memory(),
            'last_gc_timestamp': self._last_gc
        })
        
        print(f"GC completed in {gc_time}ms, free: {self._memory_stats['free_memory']} bytes")
    
    async def emergency_cleanup(self):
        """Emergency memory cleanup when critically low"""
        print("Performing emergency memory cleanup...")
        
        # Call all registered cleanup callbacks
        for callback in self._cleanup_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                print(f"Cleanup callback error: {e}")
        
        # Force aggressive garbage collection
        for _ in range(3):
            gc.collect()
            await asyncio.sleep_ms(10)
        
        final_memory = self.get_free_memory()
        print(f"Emergency cleanup complete, free memory: {final_memory} bytes")
    
    def get_free_memory(self):
        """Get available free memory in bytes"""
        print("DEBUG: Attempting to get free memory...")
        try:
            # This is a rough approximation for MicroPython
            gc.collect()
            
            # Check if gc.mem_free() exists
            if hasattr(gc, 'mem_free'):
                free_mem = gc.mem_free()
                print(f"DEBUG: ✓ gc.mem_free() returned: {free_mem} bytes")
                return free_mem
            else:
                print("DEBUG: ✗ gc.mem_free() not available")
                return 0
                
        except Exception as e:
            print(f"DEBUG: ✗ get_free_memory() exception: {type(e).__name__}: {e}")
            return 0
    
    def get_allocated_memory(self):
        """Get allocated memory in bytes"""
        print("DEBUG: Attempting to get allocated memory...")
        try:
            # Check if gc.mem_alloc() exists
            if hasattr(gc, 'mem_alloc'):
                alloc_mem = gc.mem_alloc()
                print(f"DEBUG: ✓ gc.mem_alloc() returned: {alloc_mem} bytes")
                return alloc_mem
            else:
                print("DEBUG: ✗ gc.mem_alloc() not available")
                return 0
                
        except Exception as e:
            print(f"DEBUG: ✗ get_allocated_memory() exception: {type(e).__name__}: {e}")
            return 0
    
    def get_memory_info(self):
        """Get comprehensive memory information"""
        try:
            free = self.get_free_memory()
            allocated = self.get_allocated_memory()
            total = free + allocated
            
            return {
                'free': free,
                'allocated': allocated,
                'total': total,
                'usage_percent': (allocated / total * 100) if total > 0 else 0,
                'gc_stats': self._memory_stats.copy()
            }
        except Exception as e:
            print(f"Memory info error: {e}")
            return {'error': str(e)}
    
    def print_memory_report(self):
        """Print detailed memory report"""
        info = self.get_memory_info()
        if 'error' in info:
            print(f"Memory report error: {info['error']}")
            return
        
        print("=" * 40)
        print("MEMORY REPORT")
        print("=" * 40)
        print(f"Free Memory:      {info['free']:,} bytes")
        print(f"Allocated Memory: {info['allocated']:,} bytes")
        print(f"Total Memory:     {info['total']:,} bytes")
        print(f"Usage:            {info['usage_percent']:.1f}%")
        
        if info['gc_stats']:
            print(f"Last GC Time:     {info['gc_stats'].get('last_gc_time', 'N/A')} ms")
            print(f"Last GC:          {info['gc_stats'].get('last_gc_timestamp', 'N/A')}")
        
        print("=" * 40)
    
    def is_memory_low(self):
        """Check if memory is running low"""
        return self.get_free_memory() < self.LOW_MEMORY_THRESHOLD
    
    def is_memory_critical(self):
        """Check if memory is critically low"""
        return self.get_free_memory() < self.CRITICAL_MEMORY_THRESHOLD

class ResourcePool:
    """Pool for reusing objects to reduce allocations"""
    
    def __init__(self, factory_func, max_size=10):
        self.factory_func = factory_func
        self.max_size = max_size
        self._pool = []
        self._in_use = set()
    
    def acquire(self):
        """Get an object from the pool"""
        if self._pool:
            obj = self._pool.pop()
        else:
            obj = self.factory_func()
        
        self._in_use.add(id(obj))
        return obj
    
    def release(self, obj):
        """Return an object to the pool"""
        obj_id = id(obj)
        if obj_id in self._in_use:
            self._in_use.remove(obj_id)
            
            if len(self._pool) < self.max_size:
                # Reset object state if it has a reset method
                if hasattr(obj, 'reset'):
                    obj.reset()
                self._pool.append(obj)
    
    def cleanup(self):
        """Clean up the pool"""
        self._pool.clear()
        self._in_use.clear()
        gc.collect()
    
    def get_stats(self):
        """Get pool statistics"""
        return {
            'pool_size': len(self._pool),
            'in_use': len(self._in_use),
            'max_size': self.max_size
        }

# Global memory manager instance
memory_manager = MemoryManager()