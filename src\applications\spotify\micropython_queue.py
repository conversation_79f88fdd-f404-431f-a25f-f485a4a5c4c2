"""
MicroPython-compatible async Queue implementation for ESP32 with 520KB RAM constraints.
Designed for PrestoDeck hardware with memory optimization and dual-core safety.
"""

import uasyncio as asyncio
import gc
import time
from micropython import const

# Constants for memory optimization
_MAX_QUEUE_SIZE = const(50)
_MEMORY_CHECK_INTERVAL = const(10)  # Check memory every 10 operations
_MIN_FREE_MEMORY = const(20480)  # 20KB minimum free memory

class MicroPythonQueue:
    """
    Async Queue implementation compatible with MicroPython uasyncio.
    
    Features:
    - Memory-efficient circular buffer design
    - ESP32 dual-core thread safety via asyncio primitives
    - Automatic memory management and garbage collection
    - Resource pooling to prevent heap fragmentation
    - Performance monitoring and debugging
    """
    
    def __init__(self, maxsize=_MAX_QUEUE_SIZE):
        print(f"DEBUG: Initializing MicroPythonQueue with maxsize={maxsize}")
        
        # Core queue data structures
        self._maxsize = maxsize
        self._items = [None] * maxsize  # Pre-allocate to prevent fragmentation
        self._head = 0  # Read position
        self._tail = 0  # Write position
        self._size = 0  # Current number of items
        
        # Async synchronization primitives
        self._put_event = asyncio.Event()  # Signals when space is available
        self._get_event = asyncio.Event()  # Signals when items are available
        
        # Memory and performance tracking
        self._operations_count = 0
        self._memory_warnings = 0
        self._peak_size = 0
        self._total_puts = 0
        self._total_gets = 0
        
        # Resource management
        self._closed = False
        
        print(f"DEBUG: MicroPythonQueue initialized successfully")
        
    def _check_memory(self):
        """Check memory usage and trigger GC if needed"""
        self._operations_count += 1
        
        if self._operations_count % _MEMORY_CHECK_INTERVAL == 0:
            try:
                gc.collect()
                if hasattr(gc, 'mem_free'):
                    free_mem = gc.mem_free()
                    if free_mem < _MIN_FREE_MEMORY:
                        self._memory_warnings += 1
                        print(f"DEBUG: Low memory warning: {free_mem} bytes free")
                        gc.collect()  # Force additional GC
                        return False
                return True
            except Exception as e:
                print(f"DEBUG: Memory check error: {e}")
                return True
        return True
    
    def _advance_pointer(self, pointer):
        """Advance circular buffer pointer"""
        return (pointer + 1) % self._maxsize
    
    async def put(self, item):
        """
        Put an item into the queue.
        
        Args:
            item: Item to add to queue
            
        Raises:
            QueueFull: If queue is at maximum capacity
            QueueClosed: If queue has been closed
        """
        if self._closed:
            raise QueueClosed("Queue is closed")
            
        print(f"DEBUG: Queue put() called, current size: {self._size}")
        
        # Wait for space to become available
        while self._size >= self._maxsize:
            print("DEBUG: Queue full, waiting for space...")
            self._put_event.clear()
            await self._put_event.wait()
            
            if self._closed:
                raise QueueClosed("Queue closed while waiting")
        
        # Check memory before adding item
        if not self._check_memory():
            print("DEBUG: Memory pressure detected, forcing GC")
            gc.collect()
        
        # Add item to queue
        self._items[self._tail] = item
        self._tail = self._advance_pointer(self._tail)
        self._size += 1
        self._total_puts += 1
        
        # Update peak size tracking
        if self._size > self._peak_size:
            self._peak_size = self._size
        
        # Signal that an item is available
        self._get_event.set()
        
        print(f"DEBUG: Item added to queue, new size: {self._size}")
    
    async def get(self):
        """
        Get an item from the queue.
        
        Returns:
            Item from the queue
            
        Raises:
            QueueClosed: If queue has been closed and is empty
        """
        print(f"DEBUG: Queue get() called, current size: {self._size}")
        
        # Wait for an item to become available
        while self._size == 0:
            if self._closed:
                raise QueueClosed("Queue is closed and empty")
                
            print("DEBUG: Queue empty, waiting for item...")
            self._get_event.clear()
            await self._get_event.wait()
        
        # Get item from queue
        item = self._items[self._head]
        self._items[self._head] = None  # Clear reference to help GC
        self._head = self._advance_pointer(self._head)
        self._size -= 1
        self._total_gets += 1
        
        # Signal that space is available
        self._put_event.set()
        
        # Periodic memory management
        self._check_memory()
        
        print(f"DEBUG: Item retrieved from queue, new size: {self._size}")
        return item
    
    def put_nowait(self, item):
        """
        Put an item into the queue without waiting.
        
        Args:
            item: Item to add to queue
            
        Raises:
            QueueFull: If queue is full
            QueueClosed: If queue has been closed
        """
        if self._closed:
            raise QueueClosed("Queue is closed")
            
        if self._size >= self._maxsize:
            raise QueueFull("Queue is full")
        
        # Check memory
        self._check_memory()
        
        # Add item
        self._items[self._tail] = item
        self._tail = self._advance_pointer(self._tail)
        self._size += 1
        self._total_puts += 1
        
        # Update peak size
        if self._size > self._peak_size:
            self._peak_size = self._size
        
        # Signal item available
        self._get_event.set()
        
        print(f"DEBUG: Item added via put_nowait, size: {self._size}")
    
    def get_nowait(self):
        """
        Get an item from the queue without waiting.
        
        Returns:
            Item from the queue
            
        Raises:
            QueueEmpty: If queue is empty
            QueueClosed: If queue has been closed and is empty
        """
        if self._size == 0:
            if self._closed:
                raise QueueClosed("Queue is closed and empty")
            raise QueueEmpty("Queue is empty")
        
        # Get item
        item = self._items[self._head]
        self._items[self._head] = None  # Clear reference
        self._head = self._advance_pointer(self._head)
        self._size -= 1
        self._total_gets += 1
        
        # Signal space available
        self._put_event.set()
        
        # Memory management
        self._check_memory()
        
        print(f"DEBUG: Item retrieved via get_nowait, size: {self._size}")
        return item
    
    def empty(self):
        """Check if queue is empty"""
        return self._size == 0
    
    def full(self):
        """Check if queue is full"""
        return self._size >= self._maxsize
    
    def qsize(self):
        """Get current queue size"""
        return self._size
    
    def maxsize(self):
        """Get maximum queue size"""
        return self._maxsize
    
    async def close(self):
        """Close the queue and wake up all waiting tasks"""
        print("DEBUG: Closing MicroPythonQueue")
        self._closed = True
        
        # Wake up all waiting tasks
        self._put_event.set()
        self._get_event.set()
        
        # Clear all items to help GC
        for i in range(self._maxsize):
            self._items[i] = None
        
        self._size = 0
        self._head = 0
        self._tail = 0
        
        # Force garbage collection
        gc.collect()
        
        print("DEBUG: MicroPythonQueue closed successfully")
    
    def get_stats(self):
        """Get queue statistics for monitoring"""
        try:
            free_mem = gc.mem_free() if hasattr(gc, 'mem_free') else 0
            alloc_mem = gc.mem_alloc() if hasattr(gc, 'mem_alloc') else 0
        except:
            free_mem = alloc_mem = 0
            
        return {
            'size': self._size,
            'maxsize': self._maxsize,
            'peak_size': self._peak_size,
            'total_puts': self._total_puts,
            'total_gets': self._total_gets,
            'operations_count': self._operations_count,
            'memory_warnings': self._memory_warnings,
            'free_memory': free_mem,
            'allocated_memory': alloc_mem,
            'closed': self._closed
        }
    
    def print_stats(self):
        """Print detailed queue statistics"""
        stats = self.get_stats()
        print("=" * 40)
        print("MICROPYTHON QUEUE STATISTICS")
        print("=" * 40)
        print(f"Current Size:     {stats['size']}")
        print(f"Max Size:         {stats['maxsize']}")
        print(f"Peak Size:        {stats['peak_size']}")
        print(f"Total Puts:       {stats['total_puts']}")
        print(f"Total Gets:       {stats['total_gets']}")
        print(f"Operations:       {stats['operations_count']}")
        print(f"Memory Warnings:  {stats['memory_warnings']}")
        print(f"Free Memory:      {stats['free_memory']} bytes")
        print(f"Allocated Memory: {stats['allocated_memory']} bytes")
        print(f"Closed:           {stats['closed']}")
        print("=" * 40)

# Custom exceptions
class QueueEmpty(Exception):
    """Exception raised when queue is empty"""
    pass

class QueueFull(Exception):
    """Exception raised when queue is full"""
    pass

class QueueClosed(Exception):
    """Exception raised when queue is closed"""
    pass

# Factory function to create queues
def Queue(maxsize=_MAX_QUEUE_SIZE):
    """
    Factory function to create a MicroPython-compatible async queue.
    
    Args:
        maxsize: Maximum number of items in queue
        
    Returns:
        MicroPythonQueue instance
    """
    print(f"DEBUG: Creating MicroPythonQueue with maxsize={maxsize}")
    return MicroPythonQueue(maxsize)

# Performance testing function
async def test_queue_performance():
    """Test queue performance and memory usage"""
    print("DEBUG: Starting queue performance test")
    
    queue = Queue(maxsize=20)
    start_time = time.ticks_ms()
    
    # Test putting items
    for i in range(15):
        await queue.put(f"test_item_{i}")
        await asyncio.sleep_ms(1)  # Yield control
    
    # Test getting items
    for i in range(15):
        item = await queue.get()
        print(f"Retrieved: {item}")
        await asyncio.sleep_ms(1)  # Yield control
    
    end_time = time.ticks_ms()
    duration = time.ticks_diff(end_time, start_time)
    
    queue.print_stats()
    print(f"Performance test completed in {duration} ms")
    
    await queue.close()
    return duration