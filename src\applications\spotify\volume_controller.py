import time
import uasyncio as asyncio
from micropython import const

class VolumeController:
    """Manages volume control with smooth curves, caching, and visual feedback"""
    
    # Constants for volume control
    MIN_VOLUME = const(0)
    MAX_VOLUME = const(100)
    VOLUME_STEP = const(5)
    SMOOTH_CURVE_STEPS = const(10)
    CACHE_DURATION = const(2)  # seconds
    
    def __init__(self, spotify_client, display_callback=None):
        self.spotify_client = spotify_client
        self.display_callback = display_callback
        
        # Volume state
        self._current_volume = 50
        self._target_volume = 50
        self._last_api_update = 0
        self._volume_changing = False
        self._pending_changes = []
        
        # Visual feedback state
        self._show_volume_overlay = False
        self._overlay_timeout = 0
        
        # Smooth volume change task
        self._volume_task = None
    
    async def initialize(self):
        """Initialize volume controller and sync with Spotify"""
        try:
            volume = self.spotify_client.get_volume()
            self._current_volume = volume
            self._target_volume = volume
            print(f"Volume controller initialized at {volume}%")
        except Exception as e:
            print(f"Failed to initialize volume: {e}")
    
    def get_volume(self):
        """Get current volume (cached or from API)"""
        return self._current_volume
    
    async def set_volume(self, volume, show_overlay=True):
        """Set volume with smooth transition and visual feedback"""
        volume = max(self.MIN_VOLUME, min(self.MAX_VOLUME, volume))
        
        if volume == self._target_volume:
            return
        
        self._target_volume = volume
        
        if show_overlay:
            await self._show_volume_feedback()
        
        # Start smooth volume transition
        if self._volume_task:
            self._volume_task.cancel()
        
        self._volume_task = asyncio.create_task(self._smooth_volume_change())
    
    async def adjust_volume(self, delta, show_overlay=True):
        """Adjust volume by delta amount"""
        new_volume = self._current_volume + delta
        await self.set_volume(new_volume, show_overlay)
    
    async def volume_up(self, show_overlay=True):
        """Increase volume by step amount"""
        await self.adjust_volume(self.VOLUME_STEP, show_overlay)
    
    async def volume_down(self, show_overlay=True):
        """Decrease volume by step amount"""
        await self.adjust_volume(-self.VOLUME_STEP, show_overlay)
    
    async def _smooth_volume_change(self):
        """Smoothly transition from current to target volume"""
        try:
            self._volume_changing = True
            start_volume = self._current_volume
            target_volume = self._target_volume
            
            steps = self.SMOOTH_CURVE_STEPS
            volume_diff = target_volume - start_volume
            
            for step in range(steps + 1):
                if step == steps:
                    # Ensure we hit the exact target
                    volume = target_volume
                else:
                    # Use easing curve for smooth transition
                    progress = step / steps
                    eased_progress = self._ease_in_out_cubic(progress)
                    volume = int(start_volume + (volume_diff * eased_progress))
                
                self._current_volume = volume
                
                # Update display immediately for responsiveness
                if self.display_callback:
                    self.display_callback(volume)
                
                # Send to Spotify API (async, non-blocking)
                if step == steps or step % 3 == 0:  # Reduce API calls
                    await self.spotify_client.set_volume_async(volume)
                
                await asyncio.sleep_ms(20)  # Smooth animation
            
            self._volume_changing = False
            
        except Exception as e:
            print(f"Volume change error: {e}")
            self._volume_changing = False
    
    def _ease_in_out_cubic(self, t):
        """Cubic easing function for smooth volume transitions"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    async def _show_volume_feedback(self):
        """Show volume overlay with auto-hide timer"""
        self._show_volume_overlay = True
        self._overlay_timeout = time.time() + 3.0  # Show for 3 seconds
        
        # Auto-hide task
        asyncio.create_task(self._auto_hide_overlay())
    
    async def _auto_hide_overlay(self):
        """Automatically hide volume overlay after timeout"""
        while self._show_volume_overlay and time.time() < self._overlay_timeout:
            await asyncio.sleep_ms(100)
        
        if time.time() >= self._overlay_timeout:
            self._show_volume_overlay = False
            if self.display_callback:
                self.display_callback(self._current_volume, hide_overlay=True)
    
    def is_volume_overlay_visible(self):
        """Check if volume overlay should be shown"""
        return self._show_volume_overlay
    
    def is_volume_changing(self):
        """Check if volume is currently being changed"""
        return self._volume_changing
    
    def get_volume_percentage_text(self):
        """Get formatted volume percentage for display"""
        return f"{self._current_volume}%"
    
    def get_volume_bar_width(self, max_width):
        """Calculate volume bar width for visual display"""
        return int((self._current_volume / self.MAX_VOLUME) * max_width)