# MicroPython Debug Guide for PrestoDeck ESP32

## Problem Analysis Summary

### Root Cause: `AttributeError: Queue` in asyncio module

The primary issue is that MicroPython's `uasyncio` module lacks the `Queue` class that exists in standard Python's `asyncio`. This causes the initialization failure at line 14 of `spotify_client.py`.

### Hardware Constraints
- **Platform**: ESP32-based PrestoDeck with 4MB flash, 520KB RAM
- **Memory Pressure**: Limited to 520KB RAM requires careful memory management
- **Dual-Core Architecture**: Need thread-safe operations across cores

## Solution Components

### 1. MicroPython-Compatible Queue (`micropython_queue.py`)

**Features:**
- Memory-efficient circular buffer design
- ESP32 dual-core thread safety via asyncio primitives
- Automatic memory management and garbage collection
- Resource pooling to prevent heap fragmentation
- Performance monitoring and debugging

**Usage:**
```python
from .micropython_queue import Queue

# Create queue with reduced size for memory constraints
queue = Queue(maxsize=20)  # Down from 50 for 520KB RAM

# Async operations
await queue.put(item)
item = await queue.get()

# Synchronous operations
queue.put_nowait(item)
item = queue.get_nowait()

# Memory management
queue.print_stats()
await queue.close()
```

### 2. Enhanced Spotify Client (`spotify_client.py`)

**Improvements:**
- Multi-tier Queue import strategy (MicroPython → asyncio → stub)
- Enhanced memory management with automatic cleanup
- Request processing with error recovery and rate limiting
- ESP32 dual-core safety with asyncio.Lock
- Comprehensive error handling and fallbacks

**Debug Features:**
```python
client = SpotifyWebApiClient(session)
await client.start_request_processor()

# Get performance statistics
stats = client.get_stats()
print(f"Total requests: {stats['total_requests']}")
print(f"Failed requests: {stats['failed_requests']}")

# Cleanup resources
await client.cleanup()
```

### 3. Comprehensive Test Suite (`test_micropython_compatibility.py`)

**Test Coverage:**
- Queue basic operations and edge cases
- Memory management under pressure
- Async operations and concurrency
- ESP32 dual-core safety
- Performance benchmarks
- Network operations (mocked)
- Interrupt handling
- Cleanup procedures

## Debug Procedures

### Step 1: Run Compatibility Check

```python
# Run basic compatibility detection
from .debug_compatibility import check_micropython_compatibility
check_micropython_compatibility()
```

Expected output shows which features are available:
```
✓ uasyncio import successful
✗ asyncio.to_thread NOT available
✓ asyncio.wait_for available
✗ asyncio.Queue NOT available
✓ gc.mem_free() available: 489472 bytes
```

### Step 2: Run Queue Performance Test

```python
# Test queue functionality
from .micropython_queue import test_queue_performance
import uasyncio as asyncio

async def main():
    duration = await test_queue_performance()
    print(f"Queue test completed in {duration}ms")

asyncio.run(main())
```

### Step 3: Run Full Compatibility Test Suite

```python
# Comprehensive testing
from .test_micropython_compatibility import run_compatibility_tests
import uasyncio as asyncio

async def main():
    success = await run_compatibility_tests()
    if success:
        print("All tests passed - ready for production")
    else:
        print("Some tests failed - check logs")

asyncio.run(main())
```

### Step 4: Test Spotify Client Integration

```python
# Test the enhanced Spotify client
from .spotify_client import SpotifyWebApiClient, Session
import secrets

# Mock credentials for testing
mock_credentials = {
    'access_token': 'test_token',
    'device_id': 'test_device',
    'client_id': 'test_id',
    'client_secret': 'test_secret',
    'refresh_token': 'test_refresh'
}

session = Session(mock_credentials)
client = SpotifyWebApiClient(session)

# Test initialization
await client.start_request_processor()

# Queue a test request
async def test_request():
    return {'test': 'success'}

await client._queue_request(test_request)

# Check statistics
stats = client.get_stats()
print(f"Client stats: {stats}")

# Cleanup
await client.cleanup()
```

## Memory Management Best Practices

### Memory Monitoring
```python
import gc
from .memory_manager import memory_manager

# Start memory monitoring
await memory_manager.start_monitoring()

# Check current memory
info = memory_manager.get_memory_info()
print(f"Free memory: {info['free']} bytes")
print(f"Usage: {info['usage_percent']:.1f}%")

# Force cleanup if needed
if memory_manager.is_memory_low():
    await memory_manager.emergency_cleanup()
```

### Queue Memory Optimization
```python
# Use smaller queue sizes for ESP32
queue = Queue(maxsize=15)  # Instead of 50

# Monitor queue memory usage
stats = queue.get_stats()
print(f"Peak queue size: {stats['peak_size']}")
print(f"Memory warnings: {stats['memory_warnings']}")

# Regular cleanup
await queue.close()
gc.collect()
```

## Error Recovery Strategies

### Network Timeout Handling
```python
# Enhanced error handling in network operations
try:
    if hasattr(asyncio, 'wait_for'):
        result = await asyncio.wait_for(network_operation(), timeout=10.0)
    else:
        # MicroPython fallback
        result = await network_operation()
except Exception as e:
    print(f"Network error: {e}")
    # Implement retry logic
```

### Memory Pressure Recovery
```python
# Automatic memory pressure handling
def handle_memory_pressure():
    if memory_manager.is_memory_critical():
        # Clear caches
        client._volume_cache = None
        # Force garbage collection
        gc.collect()
        # Reduce queue sizes
        queue._maxsize = min(queue._maxsize, 10)
```

### Queue Failure Recovery
```python
# Queue error recovery
try:
    await queue.put(item)
except QueueFull:
    print("Queue full, clearing old items")
    # Emergency drain
    while not queue.empty():
        queue.get_nowait()
    await queue.put(item)
except QueueClosed:
    print("Queue closed, creating new one")
    queue = Queue(maxsize=20)
    await queue.put(item)
```

## Performance Optimization

### ESP32 Dual-Core Optimization
```python
# Use locks for thread safety
if hasattr(asyncio, 'Lock'):
    lock = asyncio.Lock()
    async with lock:
        # Critical section
        await queue.put(item)
```

### Memory Pool Usage
```python
# Reuse objects to prevent fragmentation
from .memory_manager import ResourcePool

# Create object pool
def create_request_object():
    return {'url': None, 'data': None, 'callback': None}

request_pool = ResourcePool(create_request_object, max_size=10)

# Use pooled objects
request_obj = request_pool.acquire()
request_obj['url'] = 'https://api.spotify.com/v1/me/player'
# ... use object ...
request_pool.release(request_obj)
```

## Troubleshooting

### Common Issues

1. **Memory Allocation Errors**
   - Reduce queue sizes: `Queue(maxsize=10)`
   - Force garbage collection: `gc.collect()`
   - Clear caches: `client._volume_cache = None`

2. **Import Errors**
   - Check MicroPython version compatibility
   - Verify all modules are in correct paths
   - Use fallback implementations

3. **Network Timeouts**
   - Implement retry logic with exponential backoff
   - Use shorter timeouts on ESP32
   - Handle network disconnections gracefully

4. **Task Management**
   - Always cleanup async tasks: `await client.cleanup()`
   - Use proper exception handling in tasks
   - Monitor task memory usage

### Debug Output Analysis

Look for these debug messages:
- `✓ Queue initialized successfully` - Good startup
- `Low memory detected` - Memory pressure
- `Emergency processor shutdown` - Critical error
- `Queue full, dropping request` - Backpressure

### Performance Benchmarks

Target performance metrics for ESP32:
- Queue operations: < 50ms for 20 items
- Memory usage: < 50% of 520KB RAM
- Request processing: < 500ms per API call
- Startup time: < 30 seconds

## Production Deployment

1. **Pre-deployment Testing**
   ```bash
   # Run full test suite
   python test_micropython_compatibility.py
   ```

2. **Memory Configuration**
   ```python
   # Set conservative limits
   Queue.maxsize = 15
   memory_manager.LOW_MEMORY_THRESHOLD = 30720  # 30KB
   ```

3. **Monitoring Setup**
   ```python
   # Enable production monitoring
   await memory_manager.start_monitoring()
   client.start_request_processor()
   ```

4. **Error Logging**
   ```python
   # Redirect errors to file or network
   import sys
   sys.stderr = error_logger
   ```

This debug guide provides comprehensive coverage for diagnosing and resolving MicroPython compatibility issues on the ESP32 PrestoDeck platform.