# 🚨 CRITICAL FIXES REQUIRED FOR PRESTODECK SPOTIFY APP (RP2350)

## HARDWARE SPECIFICATIONS - P<PERSON>ORONI PRESTO
- **Microcontroller**: Raspberry Pi RP2350 (Pico 2)
- **SRAM**: 520KB (double the RP2040's 264KB)
- **Flash**: 4MB on-board
- **Architecture**: Dual ARM Cortex-M33 cores
- **MicroPython**: Full compatibility with standard uasyncio

## IMMEDIATE ACTION REQUIRED

### 1. RP2350 MicroPython Compatibility Check (MEDIUM PRIORITY)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 771-776, 817-830

**Status**: ✅ **LIKELY COMPATIBLE** - RP2350 MicroPython supports more asyncio features than ESP32

**Analysis**: The RP2350 runs full MicroPython with better asyncio support. However, verify compatibility:

**Verification Test**:
```python
# Test on RP2350 to confirm asyncio features:
import uasyncio as asyncio
print("to_thread available:", hasattr(asyncio, 'to_thread'))
print("wait_for available:", hasattr(asyncio, 'wait_for'))
print("create_task available:", hasattr(asyncio, 'create_task'))
```

**Fallback Fix** (if needed):
```python
# Only if verification test shows missing features:
async def fetch_state_async(self):
    try:
        # Check for RP2350 asyncio support first
        if hasattr(asyncio, 'wait_for'):
            # Use standard asyncio (likely available on RP2350)
            resp = await asyncio.wait_for(
                self.spotify_client.current_playing_async(),
                timeout=3.0
            )
        else:
            # Fallback for limited asyncio
            resp = await self.spotify_client.current_playing_async()
        # ... rest of logic
    except Exception as e:
        print(f"Async fetch state error: {e}")
        return None
```

### 2. Fix Infinite Credentials Loop (CRITICAL)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 225-231

**Problem**: Infinite loop if credentials missing - device becomes unresponsive.

**Fix**:
```python
def get_spotify_client(self):
    if not hasattr(secrets, 'SPOTIFY_CREDENTIALS') or not secrets.SPOTIFY_CREDENTIALS:
        # Show error for 10 seconds then raise exception
        for i in range(5):
            self.clear(1)
            self.display.set_pen(self.colors.WHITE)
            self.display.text("Spotify credentials not found", 40, self.height - 80, scale=.9)
            self.display.text(f"Check secrets.py ({5-i}s)", 60, self.height - 60, scale=.8)
            self.presto.update()
            time.sleep(2)
        raise Exception("Spotify credentials not configured in secrets.py")
    
    session = Session(secrets.SPOTIFY_CREDENTIALS)
    return SpotifyWebApiClient(session)
```

### 3. Fix Touch Event Conflicts (CRITICAL)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 421, 479

**Problem**: Two systems polling same touch interface causes conflicts.

**Fix**: Centralize touch processing in gesture detector:
```python
# In run() method, remove duplicate touch handler:
def run(self):
    loop = asyncio.get_event_loop()
    
    # Start all async components
    loop.create_task(self.initialize_async_components())
    # REMOVE: loop.create_task(self.touch_handler_loop())  # Remove this line
    loop.create_task(self.gesture_detector.process_touch_events())
    loop.create_task(self.display_loop())
    
    # Register button callbacks with gesture detector
    self.gesture_detector.register_button_callbacks(self.buttons)
```

### 4. Optimize Memory Management for RP2350 (MEDIUM PRIORITY)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 466-472

**Status**: ✅ **IMPROVED** - RP2350 has 520KB SRAM (vs ESP32's ~520KB total system memory)

**Analysis**: With 520KB dedicated SRAM, memory pressure is less critical, but optimization still beneficial.

**Enhanced Fix for RP2350**:
```python
def _cleanup_memory(self):
    """Memory management optimized for RP2350's 520KB SRAM"""
    try:
        gc.collect()

        if hasattr(gc, 'mem_free'):
            free_mem = gc.mem_free()
            total_sram = 520 * 1024  # 520KB SRAM on RP2350
            usage_percent = ((total_sram - free_mem) / total_sram) * 100

            print(f"SRAM usage: {usage_percent:.1f}% ({free_mem} bytes free)")

            # Conservative thresholds for RP2350's larger memory
            if free_mem < 100000:  # Less than 100KB free (emergency)
                self._image_cache.clear()
                print(f"Emergency: Cleared all cached images")
            elif free_mem < 200000:  # Less than 200KB free (aggressive)
                if len(self._image_cache) > 2:
                    keys = list(self._image_cache.keys())
                    for key in keys[:-2]:
                        del self._image_cache[key]
                    print(f"Aggressive: Limited cache to 2 images")
            elif free_mem < 300000:  # Less than 300KB free (conservative)
                if len(self._image_cache) > 3:
                    keys = list(self._image_cache.keys())
                    for key in keys[:-3]:
                        del self._image_cache[key]
                    print(f"Conservative: Limited cache to 3 images")

        gc.collect()
    except Exception as e:
        print(f"Memory cleanup error: {e}")
```

### 5. Remove Debug Logging from Production (HIGH PRIORITY)
**File**: `src/applications/spotify/spotify.py`
**Lines**: 576-720

**Problem**: Extensive debug logging in main loop causes performance issues.

**Fix**: Add debug flag:
```python
class Spotify(BaseApp):
    def __init__(self):
        super().__init__(ambient_light=True, full_res=True, layers=2)
        
        # Add debug flag
        self.debug_mode = False  # Set to True only for debugging
        
    def debug_print(self, message):
        if self.debug_mode:
            print(message)
    
    # Replace all print() calls in display_loop with self.debug_print()
```

### 6. Add Token Refresh Logic (HIGH PRIORITY)
**File**: `src/applications/spotify/spotify_client.py`

**Problem**: No token refresh - app stops working after 1 hour.

**Fix**: Add token refresh method:
```python
async def refresh_token_if_needed(self):
    """Check and refresh token if needed"""
    try:
        # Check if token is expired (implement based on your token structure)
        if self.session.is_token_expired():
            await self.session.refresh_access_token()
            print("Token refreshed successfully")
    except Exception as e:
        print(f"Token refresh failed: {e}")
```

## TESTING RECOMMENDATIONS

1. **Memory Testing**: Monitor memory usage during extended operation
2. **Network Testing**: Test with poor network conditions
3. **Touch Testing**: Verify all touch interactions work correctly
4. **Error Testing**: Test with missing credentials, network failures
5. **Performance Testing**: Measure frame rates and response times

## DEPLOYMENT CHECKLIST

- [ ] Fix MicroPython compatibility issues
- [ ] Fix infinite credentials loop
- [ ] Centralize touch event processing
- [ ] Implement memory pressure handling
- [ ] Remove/conditional debug logging
- [ ] Add token refresh logic
- [ ] Test on actual ESP32 hardware
- [ ] Verify memory usage under load
- [ ] Test error recovery scenarios
