import sys
import uasyncio as asyncio
import time
import gc

import urequests as requests
import usocket as socket
import ujson as json

# Import MicroPython-compatible Queue implementation
try:
    from .micropython_queue import Queue, QueueEmpty, QueueFull, QueueClosed
    print("DEBUG: ✓ MicroPython Queue implementation imported successfully")
except ImportError as e:
    print(f"DEBUG: ✗ Failed to import MicroPython Queue: {e}")
    # Fallback to basic list-based queue if available
    try:
        Queue = asyncio.Queue
        print("DEBUG: ✓ Fallback to standard asyncio.Queue")
    except AttributeError:
        print("DEBUG: ✗ No Queue implementation available, creating stub")
        # Create a stub Queue class as last resort
        class Queue:
            def __init__(self, maxsize=50):
                self._items = []
                self._maxsize = maxsize
                print(f"DEBUG: Using stub Queue with maxsize={maxsize}")
            
            async def put(self, item):
                if len(self._items) < self._maxsize:
                    self._items.append(item)
                else:
                    print("DEBUG: Stub queue full, dropping item")
            
            async def get(self):
                if self._items:
                    return self._items.pop(0)
                return None
            
            def empty(self):
                return len(self._items) == 0
        
        # Define stub exceptions
        class QueueEmpty(Exception): pass
        class QueueFull(Exception): pass
        class QueueClosed(Exception): pass

class SpotifyWebApiClient:
    def __init__(self, session):
        print("DEBUG: Initializing SpotifyWebApiClient with enhanced memory management")
        self.session = session
        self._volume_cache = None
        self._last_volume_update = 0
        
        # Initialize MicroPython-compatible queue with ESP32 optimizations
        try:
            self._request_queue = Queue(maxsize=20)  # Reduced size for 520KB RAM constraint
            print("DEBUG: ✓ Request queue initialized successfully")
        except Exception as e:
            print(f"DEBUG: ✗ Failed to initialize request queue: {e}")
            raise
        
        # Request processing state
        self._processing_requests = False
        self._processor_task = None
        
        # Memory and performance tracking
        self._total_requests = 0
        self._failed_requests = 0
        self._memory_cleanups = 0
        
        # ESP32 dual-core safety
        self._lock = asyncio.Lock() if hasattr(asyncio, 'Lock') else None
        
        print("DEBUG: SpotifyWebApiClient initialization complete")

    async def start_request_processor(self):
        """Start the async request processor with ESP32 optimizations"""
        print("DEBUG: Starting request processor...")
        
        if not self._processing_requests:
            self._processing_requests = True
            try:
                self._processor_task = asyncio.create_task(self._process_requests())
                print("DEBUG: ✓ Request processor task created successfully")
            except Exception as e:
                print(f"DEBUG: ✗ Failed to create processor task: {e}")
                self._processing_requests = False
                raise

    async def _process_requests(self):
        """Process queued requests with memory management and error recovery"""
        print("DEBUG: Request processor started")
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self._processing_requests:
            try:
                # Check memory before processing
                self._check_memory_usage()
                
                # Process request with timeout
                if not self._request_queue.empty():
                    print("DEBUG: Processing queued request...")
                    
                    try:
                        # Get request with timeout to prevent hanging
                        request_func, callback = await asyncio.wait_for(
                            self._request_queue.get(),
                            timeout=1.0
                        ) if hasattr(asyncio, 'wait_for') else await self._request_queue.get()
                        
                        # Execute request with error handling
                        result = await self._execute_request_safely(request_func)
                        
                        # Execute callback if provided
                        if callback and result is not None:
                            try:
                                if asyncio.iscoroutinefunction(callback):
                                    await callback(result)
                                else:
                                    callback(result)
                            except Exception as e:
                                print(f"DEBUG: Callback execution error: {e}")
                        
                        self._total_requests += 1
                        consecutive_errors = 0  # Reset error counter on success
                        
                    except Exception as e:
                        print(f"DEBUG: Request processing error: {e}")
                        self._failed_requests += 1
                        consecutive_errors += 1
                        
                        # Emergency stop if too many consecutive errors
                        if consecutive_errors >= max_consecutive_errors:
                            print("DEBUG: Too many consecutive errors, stopping processor")
                            break
                    
                    # Rate limiting for API
                    await asyncio.sleep_ms(100)
                else:
                    # No requests, yield control
                    await asyncio.sleep_ms(50)
                    
            except Exception as e:
                print(f"DEBUG: Request processor critical error: {e}")
                consecutive_errors += 1
                await asyncio.sleep_ms(200)
                
                # Emergency cleanup on critical errors
                if consecutive_errors >= max_consecutive_errors:
                    print("DEBUG: Emergency processor shutdown")
                    break
        
        print("DEBUG: Request processor stopped")
        self._processing_requests = False

    async def _execute_request_safely(self, request_func):
        """Execute request with timeout and error handling"""
        try:
            # Execute with timeout if available
            if hasattr(asyncio, 'wait_for'):
                result = await asyncio.wait_for(request_func(), timeout=10.0)
            else:
                result = await request_func()
            return result
        except Exception as e:
            print(f"DEBUG: Safe request execution failed: {e}")
            return None

    def _check_memory_usage(self):
        """Check memory usage and perform cleanup if needed"""
        try:
            if hasattr(gc, 'mem_free'):
                free_mem = gc.mem_free()
                if free_mem < 30720:  # Less than 30KB free
                    print(f"DEBUG: Low memory detected: {free_mem} bytes, performing cleanup")
                    gc.collect()
                    self._memory_cleanups += 1
        except Exception as e:
            print(f"DEBUG: Memory check error: {e}")

    async def _queue_request(self, request_func, callback=None):
        """Queue a request for async processing with error handling"""
        try:
            print("DEBUG: Queuing request...")
            
            # Use lock if available for thread safety
            if self._lock:
                async with self._lock:
                    await self._request_queue.put((request_func, callback))
            else:
                await self._request_queue.put((request_func, callback))
                
            print("DEBUG: ✓ Request queued successfully")
            
        except QueueFull:
            print("DEBUG: ✗ Request queue full, dropping request")
            self._failed_requests += 1
        except Exception as e:
            print(f"DEBUG: ✗ Failed to queue request: {e}")
            self._failed_requests += 1

    async def cleanup(self):
        """Cleanup resources and stop processing"""
        print("DEBUG: Cleaning up SpotifyWebApiClient...")
        
        # Stop request processing
        self._processing_requests = False
        
        # Wait for processor task to finish
        if self._processor_task:
            try:
                await asyncio.wait_for(self._processor_task, timeout=2.0)
            except:
                pass  # Timeout is acceptable
        
        # Close and cleanup queue
        if hasattr(self._request_queue, 'close'):
            await self._request_queue.close()
        
        # Clear caches
        self._volume_cache = None
        
        # Force garbage collection
        gc.collect()
        
        print("DEBUG: SpotifyWebApiClient cleanup complete")

    def get_stats(self):
        """Get client statistics for monitoring"""
        stats = {
            'total_requests': self._total_requests,
            'failed_requests': self._failed_requests,
            'memory_cleanups': self._memory_cleanups,
            'processing_requests': self._processing_requests,
            'volume_cache': self._volume_cache,
        }
        
        # Add queue stats if available
        if hasattr(self._request_queue, 'get_stats'):
            stats['queue_stats'] = self._request_queue.get_stats()
        
        return stats

    def play(self, context_uri=None, uris=None, offset=None, position_ms=None):
        request_body = {}
        if context_uri is not None:
            request_body['context_uri'] = context_uri
        if uris is not None:
            request_body['uris'] = list(uris)
        if offset is not None:
            request_body['offset'] = offset
        if position_ms is not None:
            request_body['position_ms'] = position_ms

        self.session.put(
            url='https://api.spotify.com/v1/me/player/play',
            json=request_body,
        )

    def pause(self):
        self.session.put(
            url='https://api.spotify.com/v1/me/player/pause',
        )
    
    def toggle_shuffle(self, state):
        value = "true" if state else "false"
        self.session.put(
            url=f'https://api.spotify.com/v1/me/player/shuffle?state={value}',
        )
    
    def toggle_repeat(self, state):
        value = "track" if state else 'off'
        self.session.put(
            url=f'https://api.spotify.com/v1/me/player/repeat?state={value}',
        )
    
    def next(self):
        self.session.post(
            url='https://api.spotify.com/v1/me/player/next',
        )

    def previous(self):
        self.session.post(
            url='https://api.spotify.com/v1/me/player/previous',
        )

    def current_playing(self):
        return self.session.get(
            url='https://api.spotify.com/v1/me/player',
        )
    
    def recently_played(self):
        return self.session.get(
            url='https://api.spotify.com/v1/me/player/recently-played?limit=1',
        )

    def set_volume(self, volume_percent):
        """Set playback volume (0-100)"""
        self._volume_cache = volume_percent
        self._last_volume_update = time.time()
        self.session.put(
            url=f'https://api.spotify.com/v1/me/player/volume?volume_percent={volume_percent}',
        )

    def get_volume(self):
        """Get cached volume or fetch from API"""
        if self._volume_cache is not None and (time.time() - self._last_volume_update) < 5:
            return self._volume_cache
        
        try:
            response = self.session.get(url='https://api.spotify.com/v1/me/player')
            if response and response.get('device'):
                volume = response['device'].get('volume_percent', 50)
                self._volume_cache = volume
                self._last_volume_update = time.time()
                return volume
        except Exception as e:
            print(f"Failed to get volume: {e}")
        
        return self._volume_cache or 50

    async def set_volume_async(self, volume_percent):
        """Set volume asynchronously with local caching"""
        self._volume_cache = volume_percent
        self._last_volume_update = time.time()
        
        async def volume_request():
            self.session.put(
                url=f'https://api.spotify.com/v1/me/player/volume?volume_percent={volume_percent}',
            )
        
        await self._queue_request(volume_request)

    async def play_async(self, context_uri=None, uris=None, offset=None, position_ms=None):
        """Async version of play"""
        async def play_request():
            self.play(context_uri, uris, offset, position_ms)
        await self._queue_request(play_request)

    async def pause_async(self):
        """Async version of pause"""
        async def pause_request():
            self.pause()
        await self._queue_request(pause_request)

    async def next_async(self):
        """Async version of next"""
        async def next_request():
            self.next()
        await self._queue_request(next_request)

    async def previous_async(self):
        """Async version of previous"""
        async def previous_request():
            self.previous()
        await self._queue_request(previous_request)

    async def current_playing_async(self):
        """Async version of current_playing with caching"""
        print("DEBUG: Entering current_playing_async()")
        try:
            # Check if asyncio.to_thread exists (not available in MicroPython)
            if hasattr(asyncio, 'to_thread'):
                print("DEBUG: Using asyncio.to_thread() - standard Python")
                response = await asyncio.wait_for(
                    asyncio.create_task(asyncio.to_thread(self.current_playing)),
                    timeout=5.0
                )
            else:
                print("DEBUG: asyncio.to_thread() not available - using MicroPython fallback")
                # MicroPython fallback: yield control instead of threading
                async def current_playing_wrapper():
                    await asyncio.sleep_ms(0)  # Yield control
                    return self.current_playing()
                
                if hasattr(asyncio, 'wait_for'):
                    response = await asyncio.wait_for(current_playing_wrapper(), timeout=5.0)
                else:
                    print("DEBUG: asyncio.wait_for() not available - direct call")
                    response = await current_playing_wrapper()
            
            print(f"DEBUG: current_playing_async() completed successfully")
            return response
            
        except Exception as e:
            print(f"DEBUG: current_playing_async() exception: {type(e).__name__}: {e}")
            # Try synchronous fallback
            try:
                print("DEBUG: Attempting synchronous fallback")
                return self.current_playing()
            except Exception as fallback_e:
                print(f"DEBUG: Synchronous fallback failed: {fallback_e}")
                return None

class Device:
    def __init__(
        self,
        id,
        is_active,
        is_private_session,
        is_restricted,
        name,
        type,
        volume_percent,
        **kwargs
    ):
        self.id = id
        self.is_active = is_active
        self.is_private_session = is_private_session
        self.is_restricted = is_restricted
        self.name = name
        self.type = type
        self.volume_percent = volume_percent

    def __repr__(self):
        return 'Device(name={}, type={}, id={})'.format(self.name, self.type, self.id)

class Session:
    def __init__(self, credentials):
        self.credentials = credentials
        self.device_id = credentials['device_id']
        if 'access_token' not in credentials:
            self._refresh_access_token()

    def get(self, url, **kwargs):
        def get_request():
            return requests.get(
                url,
                headers=self._headers(),
                **kwargs,
            )

        return self._execute_request(get_request)

    def put(self, url, json=None, **kwargs):
        # Workaround for urequests not sending "Content-Length" on empty data
        if json is None:
            json = {}

        def put_request():
            return requests.put(
                url=self._add_device_id(url),
                headers=self._headers(),
                json=json,
                **kwargs,
            )

        return self._execute_request(put_request)
    
    def post(self, url, json=None, **kwargs):
        # Workaround for urequests not sending "Content-Length" on empty data
        if json is None:
            json = {}

        def post_request():
            return requests.post(
                url=self._add_device_id(url),
                headers=self._headers(),
                json=json,
                **kwargs,
            )

        return self._execute_request(post_request)

    def _headers(self):
        return {'Authorization': 'Bearer {access_token}'.format(**self.credentials)}

    def _execute_request(self, request):
        response = request()
        if response.status_code == 401:
            error = Session._error_from_response(response)

            if error['message'] == 'The access token expired':
                self._refresh_access_token()
                response = request()  # Retry

        self._check_status_code(response)
        content_type = response.headers.get("content-type")
        if response.content and content_type and content_type.startswith("application/json"):
            resp_json = response.json()
            response.close()
            return resp_json
        response.close()
        

    @staticmethod
    def _check_status_code(response):
        if response.status_code >= 400:
            error = Session._error_from_response(response)
            response.close()
            raise SpotifyWebApiError(**error)

    @staticmethod
    def _error_from_response(response):
        try:
            error = response.json()['error']
            message = error['message']
            reason = error.get('reason')
        except (ValueError, KeyError):
            message = response.text
            reason = None
        return {'message': message, 'status': response.status_code, 'reason': reason}

    def _add_device_id(self, url):
        join = '&' if '?' in url else '?'
        return '{path}{join}device_id={device_id}'.format(path=url, join=join, device_id=self.device_id) if self.device_id else url

    def _refresh_access_token(self):
        token_endpoint = "https://accounts.spotify.com/api/token"
        params = dict(
            grant_type="refresh_token",
            refresh_token=self.credentials['refresh_token'],
            client_id=self.credentials['client_id'],
            client_secret=self.credentials['client_secret'],
        )
        retries = 3
        while retries:
            try:
                response = requests.post(
                    token_endpoint,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'},
                    data=urlencode(params),
                )
                self._check_status_code(response)
                retries -= 1
            except Exception as E:
                retries -= 1
                if retries:
                    print("Failed to refresh access token, retrying")
                else:
                    print("Failed to refresh access token after 3 tries, giving up")

        tokens = response.json()
        response.close()
        self.credentials['access_token'] = tokens['access_token']
        if 'refresh_token' in tokens:
            self.credentials['refresh_token'] = tokens['refresh_token']

class SpotifyWebApiError(Exception):
    def __init__(self, message, status=None, reason=None):
        super().__init__(message)
        self.status = status
        self.reason = reason

def quote(s):
    always_safe = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' 'abcdefghijklmnopqrstuvwxyz' '0123456789' '_.-'
    res = []
    for c in s:
        if c in always_safe:
            res.append(c)
            continue
        res.append('%%%x' % ord(c))
    return ''.join(res)

def quote_plus(s):
    s = quote(s)
    if ' ' in s:
        s = s.replace(' ', '+')
    return s

def unquote(s):
    res = s.split('%')
    for i in range(1, len(res)):
        item = res[i]
        try:
            res[i] = chr(int(item[:2], 16)) + item[2:]
        except ValueError:
            res[i] = '%' + item
    return "".join(res)

def urlencode(query):
    if isinstance(query, dict):
        query = query.items()
    li = []
    for k, v in query:
        if not isinstance(v, list):
            v = [v]
        for value in v:
            k = quote_plus(str(k))
            v = quote_plus(str(value))
            li.append(k + '=' + v)
    return '&'.join(li)
