# 🐛 DEBUG GUIDE - CRITICAL ISSUES ANALYSIS

## 🚨 ISSUE #1: MicroPython Compatibility Failure

### **Symptoms**:
- Application crashes on ESP32 with `AttributeError: 'module' object has no attribute 'to_thread'`
- Error occurs during API calls in `fetch_state_async()` and `get_album_cover_async()`

### **Root Cause Analysis**:
```python
# PROBLEMATIC CODE (Lines 771-776 in spotify.py):
if hasattr(asyncio, 'to_thread') and hasattr(asyncio, 'wait_for'):
    resp = await asyncio.wait_for(
        asyncio.create_task(asyncio.to_thread(self.spotify_client.recently_played)),
        timeout=3.0
    )
```

**Problem**: MicroPython's `uasyncio` module is a subset of standard Python's `asyncio`. It lacks:
- `asyncio.to_thread()` - Doesn't exist
- `asyncio.wait_for()` - Limited implementation
- `asyncio.create_task()` - Different behavior

### **Debug Steps**:
1. **Verify MicroPython Environment**:
   ```python
   import uasyncio as asyncio
   print(dir(asyncio))  # Check available functions
   ```

2. **Test Async Compatibility**:
   ```python
   # This will fail on MicroPython:
   hasattr(asyncio, 'to_thread')  # Returns False
   hasattr(asyncio, 'wait_for')   # May return False
   ```

### **Fix Implementation**:
```python
async def fetch_state_async(self):
    try:
        # Direct async call - no threading needed
        resp = await self.spotify_client.current_playing_async()
        if resp and resp.get("item"):
            # Process response...
            return device_id, current_track, is_playing, shuffle, repeat, volume
        
        # Fallback to recently played - direct call
        await asyncio.sleep_ms(0)  # Yield control
        resp = self.spotify_client.recently_played()  # Synchronous call
        if resp and resp.get("items"):
            current_track = resp["items"][0]["track"]
            return None, current_track, False, False, False, None
            
    except Exception as e:
        print(f"Fetch state error: {e}")
        return None
```

---

## 🚨 ISSUE #2: Infinite Credentials Loop

### **Symptoms**:
- Device becomes completely unresponsive
- Display shows "Spotify credentials not found" indefinitely
- No way to exit or recover without hardware reset

### **Root Cause Analysis**:
```python
# PROBLEMATIC CODE (Lines 225-231):
def get_spotify_client(self):
    if not hasattr(secrets, 'SPOTIFY_CREDENTIALS') or not secrets.SPOTIFY_CREDENTIALS:
        while True:  # ← INFINITE LOOP!
            self.clear(1)
            self.display.set_pen(self.colors.WHITE)
            self.display.text("Spotify credentials not found", 40, self.height - 80, scale=.9)
            self.presto.update()
            time.sleep(2)
```

**Problem**: No exit condition from while loop if credentials are missing.

### **Debug Steps**:
1. **Check Secrets Configuration**:
   ```python
   import secrets
   print(hasattr(secrets, 'SPOTIFY_CREDENTIALS'))
   print(getattr(secrets, 'SPOTIFY_CREDENTIALS', None))
   ```

2. **Simulate Missing Credentials**:
   ```python
   # Temporarily rename SPOTIFY_CREDENTIALS in secrets.py
   # Run application to reproduce infinite loop
   ```

### **Fix Implementation**:
```python
def get_spotify_client(self):
    if not hasattr(secrets, 'SPOTIFY_CREDENTIALS') or not secrets.SPOTIFY_CREDENTIALS:
        # Show error message with countdown
        for countdown in range(10, 0, -1):
            self.clear(1)
            self.display.set_pen(self.colors.WHITE)
            self.display.text("Spotify credentials not found!", 40, self.height - 100, scale=.9)
            self.display.text("Check secrets.py file", 60, self.height - 80, scale=.8)
            self.display.text(f"Exiting in {countdown}s...", 80, self.height - 60, scale=.7)
            self.presto.update()
            time.sleep(1)
        
        # Exit gracefully instead of hanging
        raise Exception("SPOTIFY_CREDENTIALS not configured in secrets.py")
    
    session = Session(secrets.SPOTIFY_CREDENTIALS)
    return SpotifyWebApiClient(session)
```

---

## 🚨 ISSUE #3: Touch Event Conflicts

### **Symptoms**:
- Erratic touch behavior
- Missed button presses
- Gestures not detected properly
- Touch events firing multiple times

### **Root Cause Analysis**:
```python
# CONFLICTING SYSTEMS:
# System 1: Button-based touch handling (Line 479)
async def touch_handler_loop(self):
    while not self.state.exit:
        self.touch.poll()  # ← Polling touch interface
        # Process button presses...

# System 2: Gesture detection (Line 421)  
async def process_touch_events(self):
    while True:
        self.touch.poll()  # ← SAME interface being polled!
        # Process gestures...
```

**Problem**: Two async tasks polling the same touch interface simultaneously.

### **Debug Steps**:
1. **Monitor Touch Polling**:
   ```python
   # Add debug prints to both touch handlers
   print(f"Button handler: {self.touch.x}, {self.touch.y}, {self.touch.state}")
   print(f"Gesture handler: {self.touch.x}, {self.touch.y}, {self.touch.state}")
   ```

2. **Check Task Execution**:
   ```python
   # Verify both tasks are running
   import uasyncio as asyncio
   print(f"Active tasks: {len(asyncio.current_task())}")
   ```

### **Fix Implementation**:
```python
# SOLUTION: Centralize touch processing in gesture detector

class GestureDetector:
    def __init__(self, touch, width, height):
        # ... existing code ...
        self._button_callbacks = []
    
    def register_button_callbacks(self, buttons):
        """Register button callbacks with gesture detector"""
        self._button_callbacks = buttons
    
    async def process_touch_events(self):
        """Unified touch processing for both gestures and buttons"""
        while True:
            try:
                self.touch.poll()
                current_time = time.ticks_ms()
                
                # Process gestures first
                if self.touch.state:
                    await self._handle_touch_down(current_time)
                else:
                    await self._handle_touch_up(current_time)
                
                # Process button presses if no gesture detected
                if not self._gesture_detected:
                    await self._process_button_presses()
                
                await asyncio.sleep_ms(10)
                
            except Exception as e:
                print(f"Touch processing error: {e}")
                await asyncio.sleep_ms(50)
    
    async def _process_button_presses(self):
        """Process button presses using current touch state"""
        for button in self._button_callbacks:
            if button.enabled and button.button.is_pressed():
                # Execute button callback
                await button.on_press()

# In Spotify.run() method:
def run(self):
    loop = asyncio.get_event_loop()
    
    # Register buttons with gesture detector
    self.gesture_detector.register_button_callbacks(self.buttons)
    
    # Start unified touch processing (remove duplicate handler)
    loop.create_task(self.initialize_async_components())
    loop.create_task(self.gesture_detector.process_touch_events())  # Only this one
    loop.create_task(self.display_loop())
    # REMOVE: loop.create_task(self.touch_handler_loop())  # Remove duplicate
```

---

## 🚨 ISSUE #4: Memory Pressure and Cache Management

### **Symptoms**:
- `MemoryError` exceptions during operation
- Application becomes sluggish over time
- Sudden crashes after loading multiple album covers

### **Root Cause Analysis**:
```python
# PROBLEMATIC CACHE LOGIC (Lines 466-472):
def _cleanup_memory(self):
    if memory_manager.is_memory_low():
        cache_size = len(self._image_cache)
        if cache_size > 1:
            # Only removes oldest entries, doesn't check memory pressure
            keys = list(self._image_cache.keys())
            for key in keys[:-1]:
                del self._image_cache[key]
```

**Problem**: 
- Album covers are ~100KB each
- ESP32 has only 520KB total RAM
- Cache cleanup is too conservative

### **Debug Steps**:
1. **Monitor Memory Usage**:
   ```python
   import gc
   print(f"Free memory: {gc.mem_free()}")
   print(f"Allocated: {gc.mem_alloc()}")
   print(f"Cache size: {len(self._image_cache)}")
   ```

2. **Test Memory Pressure**:
   ```python
   # Load multiple album covers and monitor memory
   for i in range(5):
       # Load different tracks
       print(f"Memory after {i} covers: {gc.mem_free()}")
   ```

### **Fix Implementation**:
```python
def _cleanup_memory(self):
    """Aggressive memory management for ESP32 constraints"""
    try:
        gc.collect()  # Force GC first
        
        if hasattr(gc, 'mem_free'):
            free_mem = gc.mem_free()
            total_mem = gc.mem_alloc() + free_mem
            usage_percent = (gc.mem_alloc() / total_mem) * 100
            
            print(f"Memory usage: {usage_percent:.1f}% ({free_mem} bytes free)")
            
            # Emergency cleanup - less than 50KB free
            if free_mem < 50000:
                print("EMERGENCY: Clearing all caches")
                self._image_cache.clear()
                self.display_manager._text_cache.clear()
                gc.collect()
                
            # Aggressive cleanup - less than 100KB free  
            elif free_mem < 100000:
                print("AGGRESSIVE: Keeping only 1 cached image")
                if len(self._image_cache) > 1:
                    # Keep only the most recent image
                    keys = list(self._image_cache.keys())
                    for key in keys[:-1]:
                        del self._image_cache[key]
                
            # Conservative cleanup - less than 150KB free
            elif free_mem < 150000 and len(self._image_cache) > 2:
                print("CONSERVATIVE: Limiting cache to 2 images")
                keys = list(self._image_cache.keys())
                for key in keys[:-2]:
                    del self._image_cache[key]
        
        gc.collect()  # Final cleanup
        
    except Exception as e:
        print(f"Memory cleanup error: {e}")
        # Emergency fallback
        try:
            self._image_cache.clear()
            gc.collect()
        except:
            pass
```

## 🔧 DEBUGGING TOOLS AND TECHNIQUES

### **Memory Monitoring**:
```python
def print_memory_stats():
    import gc
    gc.collect()
    print(f"Free: {gc.mem_free()}, Allocated: {gc.mem_alloc()}")
    print(f"Cache items: {len(self._image_cache)}")
```

### **Task Monitoring**:
```python
def print_task_stats():
    import uasyncio as asyncio
    try:
        print(f"Current task: {asyncio.current_task()}")
    except:
        print("Task monitoring not available")
```

### **Performance Profiling**:
```python
def profile_function(func):
    start_time = time.ticks_ms()
    result = func()
    duration = time.ticks_diff(time.ticks_ms(), start_time)
    print(f"{func.__name__} took {duration}ms")
    return result
```

These fixes address the most critical issues preventing the application from running reliably on ESP32 hardware. Implement them in order of priority for best results.
