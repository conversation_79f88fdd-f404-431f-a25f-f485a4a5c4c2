"""
Spotify App Asyncio Compatibility Test
Tests specific asyncio patterns used by the Spotify application on RP2350.
"""

import gc
import time
import uasyncio as asyncio
from micropython import const

class SpotifyAsyncioTester:
    """Test asyncio patterns specific to Spotify app"""
    
    def __init__(self):
        self.results = {}
    
    def log_test(self, name, passed, details=""):
        """Log test result"""
        self.results[name] = {'passed': passed, 'details': details}
        status = "✅" if passed else "❌"
        print(f"{status} {name}: {details}")
    
    async def test_concurrent_tasks(self):
        """Test multiple concurrent tasks like in Spotify app"""
        try:
            # Simulate Spotify app's concurrent tasks
            tasks_completed = []
            
            async def mock_display_loop():
                for i in range(5):
                    await asyncio.sleep_ms(20)
                tasks_completed.append("display")
            
            async def mock_touch_handler():
                for i in range(3):
                    await asyncio.sleep_ms(30)
                tasks_completed.append("touch")
            
            async def mock_gesture_detector():
                for i in range(4):
                    await asyncio.sleep_ms(25)
                tasks_completed.append("gesture")
            
            async def mock_api_calls():
                for i in range(2):
                    await asyncio.sleep_ms(50)
                tasks_completed.append("api")
            
            # Start all tasks concurrently (like Spotify app does)
            start_time = time.ticks_ms()
            
            await asyncio.gather(
                mock_display_loop(),
                mock_touch_handler(), 
                mock_gesture_detector(),
                mock_api_calls()
            )
            
            elapsed = time.ticks_diff(time.ticks_ms(), start_time)
            
            # Should complete in reasonable time (all tasks run concurrently)
            expected_max = 200  # Should be much less than sum of all sleeps
            success = len(tasks_completed) == 4 and elapsed < expected_max
            
            self.log_test("Concurrent Tasks", success, 
                         f"Completed {len(tasks_completed)}/4 tasks in {elapsed}ms")
            return success
            
        except Exception as e:
            self.log_test("Concurrent Tasks", False, str(e))
            return False
    
    async def test_touch_synchronization(self):
        """Test touch synchronization pattern used in Spotify app"""
        try:
            # Test asyncio.Lock for touch synchronization
            touch_lock = asyncio.Lock()
            access_order = []
            
            async def touch_handler():
                async with touch_lock:
                    access_order.append("handler")
                    await asyncio.sleep_ms(20)
            
            async def gesture_detector():
                async with touch_lock:
                    access_order.append("gesture")
                    await asyncio.sleep_ms(20)
            
            # Start both simultaneously
            await asyncio.gather(
                touch_handler(),
                gesture_detector()
            )
            
            # Should have executed sequentially due to lock
            success = len(access_order) == 2
            self.log_test("Touch Synchronization", success,
                         f"Access order: {access_order}")
            return success
            
        except Exception as e:
            self.log_test("Touch Synchronization", False, str(e))
            return False
    
    async def test_api_timeout_pattern(self):
        """Test API timeout pattern used in Spotify app"""
        try:
            # Test wait_for pattern (if available)
            if hasattr(asyncio, 'wait_for'):
                async def mock_api_call():
                    await asyncio.sleep_ms(100)
                    return {"status": "success"}
                
                # Test successful call
                result = await asyncio.wait_for(mock_api_call(), timeout=0.2)
                success1 = result["status"] == "success"
                
                # Test timeout
                async def slow_api_call():
                    await asyncio.sleep_ms(300)
                    return {"status": "success"}
                
                try:
                    await asyncio.wait_for(slow_api_call(), timeout=0.1)
                    success2 = False  # Should have timed out
                except asyncio.TimeoutError:
                    success2 = True  # Expected timeout
                
                success = success1 and success2
                self.log_test("API Timeout (wait_for)", success,
                             f"Success: {success1}, Timeout: {success2}")
            else:
                # Fallback test without wait_for
                async def mock_api_call():
                    await asyncio.sleep_ms(50)
                    return {"status": "success"}
                
                result = await mock_api_call()
                success = result["status"] == "success"
                self.log_test("API Timeout (fallback)", success,
                             "wait_for not available, using fallback")
            
            return success
            
        except Exception as e:
            self.log_test("API Timeout Pattern", False, str(e))
            return False
    
    async def test_queue_operations(self):
        """Test queue operations used by Spotify client"""
        try:
            # Test if asyncio.Queue is available
            if hasattr(asyncio, 'Queue'):
                queue = asyncio.Queue(maxsize=5)
                
                # Test basic operations
                await queue.put("request1")
                await queue.put("request2")
                
                item1 = await queue.get()
                item2 = await queue.get()
                
                success = item1 == "request1" and item2 == "request2"
                self.log_test("Queue Operations (asyncio)", success,
                             f"Retrieved: {item1}, {item2}")
            else:
                # Test custom queue implementation
                try:
                    from .micropython_queue import Queue
                    queue = Queue(maxsize=5)
                    
                    await queue.put("request1")
                    await queue.put("request2")
                    
                    item1 = await queue.get()
                    item2 = await queue.get()
                    
                    success = item1 == "request1" and item2 == "request2"
                    self.log_test("Queue Operations (custom)", success,
                                 f"Retrieved: {item1}, {item2}")
                except ImportError:
                    self.log_test("Queue Operations", False,
                                 "No queue implementation available")
                    return False
            
            return success
            
        except Exception as e:
            self.log_test("Queue Operations", False, str(e))
            return False
    
    async def test_memory_during_async(self):
        """Test memory behavior during async operations"""
        try:
            initial_free = gc.mem_free()
            
            # Simulate memory-intensive async operations
            async def memory_task():
                data = bytearray(50 * 1024)  # 50KB
                await asyncio.sleep_ms(10)
                return len(data)
            
            # Run multiple memory tasks concurrently
            tasks = [memory_task() for _ in range(3)]
            results = await asyncio.gather(*tasks)
            
            # Clean up
            gc.collect()
            final_free = gc.mem_free()
            
            # Memory should be recovered
            recovery_ratio = final_free / initial_free
            success = recovery_ratio > 0.9 and len(results) == 3
            
            self.log_test("Memory During Async", success,
                         f"Recovery: {recovery_ratio:.2f}")
            return success
            
        except Exception as e:
            self.log_test("Memory During Async", False, str(e))
            return False
    
    async def test_error_handling(self):
        """Test error handling in async context"""
        try:
            errors_caught = 0
            
            async def failing_task():
                await asyncio.sleep_ms(10)
                raise ValueError("Test error")
            
            async def success_task():
                await asyncio.sleep_ms(10)
                return "success"
            
            # Test individual error handling
            try:
                await failing_task()
            except ValueError:
                errors_caught += 1
            
            # Test gather with error
            try:
                results = await asyncio.gather(
                    success_task(),
                    failing_task(),
                    return_exceptions=True
                )
                # Should have one success and one exception
                if len(results) == 2 and isinstance(results[1], ValueError):
                    errors_caught += 1
            except:
                pass
            
            success = errors_caught >= 1
            self.log_test("Error Handling", success,
                         f"Caught {errors_caught} errors")
            return success
            
        except Exception as e:
            self.log_test("Error Handling", False, str(e))
            return False
    
    async def run_spotify_tests(self):
        """Run all Spotify-specific asyncio tests"""
        print("🎵 Spotify App Asyncio Compatibility Tests")
        print("=" * 50)
        
        tests = [
            self.test_concurrent_tasks(),
            self.test_touch_synchronization(),
            self.test_api_timeout_pattern(),
            self.test_queue_operations(),
            self.test_memory_during_async(),
            self.test_error_handling()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # Count successes
        passed = sum(1 for r in results if r is True)
        total = len(tests)
        
        print("\n" + "=" * 50)
        print(f"🎵 SPOTIFY ASYNCIO TEST SUMMARY")
        print(f"Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🟢 EXCELLENT: Spotify app should run perfectly on RP2350")
        elif passed >= total * 0.8:
            print("\n🟡 GOOD: Spotify app should run well with minor issues")
        else:
            print("\n🔴 ISSUES: Spotify app may have significant problems")
        
        return self.results

# Main execution
async def main():
    """Run Spotify asyncio tests"""
    tester = SpotifyAsyncioTester()
    return await tester.run_spotify_tests()

if __name__ == "__main__":
    try:
        results = asyncio.run(main())
        print("\n✅ Spotify asyncio tests completed")
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
