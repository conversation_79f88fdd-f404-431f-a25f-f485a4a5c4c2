# Debug compatibility checker for MicroPython issues
import sys
import gc
import time

def check_micropython_compatibility():
    """Check which MicroPython features are available and log results"""
    print("=" * 50)
    print("MICROPYTHON COMPATIBILITY CHECK")
    print("=" * 50)
    
    # Check asyncio features
    print("ASYNCIO FEATURES:")
    try:
        import uasyncio as asyncio
        print("✓ uasyncio import successful")
        
        # Check specific asyncio functions
        if hasattr(asyncio, 'to_thread'):
            print("✓ asyncio.to_thread available")
        else:
            print("✗ asyncio.to_thread NOT available")
            
        if hasattr(asyncio, 'wait_for'):
            print("✓ asyncio.wait_for available")
        else:
            print("✗ asyncio.wait_for NOT available")
            
        if hasattr(asyncio, 'create_task'):
            print("✓ asyncio.create_task available")
        else:
            print("✗ asyncio.create_task NOT available")
            
        if hasattr(asyncio, 'Queue'):
            print("✓ asyncio.Queue available")
        else:
            print("✗ asyncio.Queue NOT available")
            
    except ImportError as e:
        print(f"✗ asyncio import failed: {e}")
    
    print()
    
    # Check memory management
    print("MEMORY MANAGEMENT:")
    try:
        print("✓ gc module available")
        
        if hasattr(gc, 'mem_free'):
            free_mem = gc.mem_free()
            print(f"✓ gc.mem_free() available: {free_mem} bytes")
        else:
            print("✗ gc.mem_free() NOT available")
            
        if hasattr(gc, 'mem_alloc'):
            alloc_mem = gc.mem_alloc()
            print(f"✓ gc.mem_alloc() available: {alloc_mem} bytes")
        else:
            print("✗ gc.mem_alloc() NOT available")
            
    except Exception as e:
        print(f"✗ Memory check error: {e}")
    
    print()
    
    # Check micropython specific functions
    print("MICROPYTHON FEATURES:")
    try:
        from micropython import const
        print("✓ micropython.const available")
    except ImportError:
        print("✗ micropython.const NOT available")
        
    try:
        from micropython import mem_info
        print("✓ micropython.mem_info available")
        mem_info()
    except ImportError:
        print("✗ micropython.mem_info NOT available")
    except Exception as e:
        print(f"✗ micropython.mem_info error: {e}")
    
    print()
    
    # Check time functions
    print("TIME FUNCTIONS:")
    try:
        current_time = time.time()
        print(f"✓ time.time() available: {current_time}")
    except Exception as e:
        print(f"✗ time.time() error: {e}")
        
    try:
        ticks = time.ticks_ms()
        print(f"✓ time.ticks_ms() available: {ticks}")
    except Exception as e:
        print(f"✗ time.ticks_ms() error: {e}")
    
    print()
    
    # Check HTTP libraries
    print("HTTP LIBRARIES:")
    try:
        import urequests
        print("✓ urequests available")
    except ImportError:
        print("✗ urequests NOT available")
        
    try:
        import usocket
        print("✓ usocket available")
    except ImportError:
        print("✗ usocket NOT available")
        
    try:
        import ujson
        print("✓ ujson available")
    except ImportError:
        print("✗ ujson NOT available")
    
    print("=" * 50)
    print("COMPATIBILITY CHECK COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    check_micropython_compatibility()