# Debug: Start import tracking for spotify.py
print("DEBUG: Starting spotify.py imports...")

import gc
print("DEBUG: [OK] gc imported")

import time
print("DEBUG: [OK] time imported")

import jpegdec
print("DEBUG: [OK] jpegdec imported")

import pngdec
print("DEBUG: [OK] pngdec imported")

import uasyncio as asyncio
print("DEBUG: [OK] uasyncio imported")

import urequests as requests
print("DEBUG: [OK] urequests imported")

from touch import Button
print("DEBUG: [OK] Button imported from touch")

try:
    from applications.spotify.spotify_client import Session, SpotifyWebApiClient
    print("DEBUG: [OK] spotify_client imported")
except ImportError as e:
    print(f"DEBUG: ✗ spotify_client import failed: {e}")
    raise

try:
    from applications.spotify.volume_controller import VolumeController
    print("DEBUG: [OK] volume_controller imported")
except ImportError as e:
    print(f"DEBUG: ✗ volume_controller import failed: {e}")
    raise

try:
    from applications.spotify.gesture_detector import GestureDetector, ButtonDebouncer
    print("DEBUG: [OK] gesture_detector imported")
except ImportError as e:
    print(f"DEBUG: ✗ gesture_detector import failed: {e}")
    raise

try:
    from applications.spotify.display_manager import DisplayManager
    print("DEBUG: [OK] display_manager imported")
except ImportError as e:
    print(f"DEBUG: ✗ display_manager import failed: {e}")
    raise

try:
    from applications.spotify.memory_manager import memory_manager
    print("DEBUG: [OK] memory_manager imported")
except ImportError as e:
    print(f"DEBUG: ✗ memory_manager import failed: {e}")
    raise

from base import BaseApp
print("DEBUG: [OK] BaseApp imported")

import secrets
print("DEBUG: [OK] secrets imported")

print("DEBUG: All spotify.py imports completed successfully")

class State:
    """Tracks the current state of the Spotify app including playback and UI controls."""
    def __init__(self):
        self.toggle_leds = True
        self.is_playing = False
        self.repeat = False
        self.shuffle = False
        self.track = None
        self.show_controls = False
        self.exit = False
        self.volume = 50
        self.show_volume_overlay = False

        self.latest_fetch = None
        self._state_hash = None
    
    def copy(self):
        state = State()
        state.toggle_leds = self.toggle_leds
        state.is_playing = self.is_playing
        state.repeat = self.repeat
        state.shuffle = self.shuffle
        state.show_controls = self.show_controls
        state.exit = self.exit
        state.volume = self.volume
        state.show_volume_overlay = self.show_volume_overlay
        state.track = {'id': self.track['id']} if self.track else None # only care about track id
        return state
    
    def get_hash(self):
        """Get hash for efficient state comparison"""
        if self._state_hash is None:
            self._state_hash = hash((
                self.toggle_leds, self.is_playing, self.repeat, self.shuffle,
                self.show_controls, self.exit, self.volume, self.show_volume_overlay,
                (self.track or {}).get('id')
            ))
        return self._state_hash
    
    def invalidate_hash(self):
        """Invalidate cached hash when state changes"""
        self._state_hash = None
    
    def __eq__(self, other):
        if not isinstance(other, State) or other is None:
            return False
        return self.get_hash() == other.get_hash()

class ControlButton():
    """Represents a control button with an icon and touch area."""
    def __init__(self, display, name, icons, bounds, on_press=None, update=None):
        self.name = name
        self.enabled = False
        self.icon = icons[0] if icons else None
        self.pngs = {}
        if icons:
            for icon in icons:
                png = pngdec.PNG(display)
                png.open_file("applications/spotify/icons/" + icon)
                self.pngs[icon] = png

        self.button = Button(*bounds)
        self.on_press = on_press
        self.update = update

    def is_pressed(self, state):
        """Checks if the button is enabled and currently pressed."""
        return self.enabled and self.button.is_pressed()
    
    def draw(self, state):
        """Draws the button icon if enabled."""
        if self.enabled and self.icon:
            self.draw_icon()

    def draw_icon(self):
        """Renders the button's icon centered inside its bounds."""
        png = self.pngs[self.icon]
        x, y, width, height = self.button.bounds
        png_width, png_height = png.get_width(), png.get_height()
        x_offset = (width-png_width)//2
        y_offset = (height-png_height)//2

        png.decode(x+x_offset, y+y_offset)

class Spotify(BaseApp):
    """Main Spotify app managing playback controls, track display, and UI interactions."""
    def __init__(self):
        super().__init__(ambient_light=True, full_res=True, layers=2)

        # Initialize display manager first
        self.display_manager = DisplayManager(self.display, self.presto, self.colors, self.width, self.height)
        
        # Button debouncer for reliable input
        self.button_debouncer = ButtonDebouncer(debounce_time_ms=50)
        
        # Show startup screen
        self.display.set_layer(0)
        icon = pngdec.PNG(self.display)
        icon.open_file("applications/spotify/icon.png")
        icon.decode(self.center_x - icon.get_width()//2, self.center_y - icon.get_height()//2 - 20)
        self.presto.update()

        self.display.set_font("sans")
        self.display.set_layer(1)
        self.display_text("Connecting to WIFI", (90, self.height - 80), thickness=2)
        self.presto.update()

        # Connect to WiFi with timeout
        self.presto.connect()
        wifi_timeout = time.time() + 30  # 30 second timeout
        while not self.presto.wifi.isconnected() and time.time() < wifi_timeout:
            self.clear(1)
            self.display_text("Connecting to WiFi...", (70, self.height - 80), thickness=2)
            time.sleep(1)
        
        if not self.presto.wifi.isconnected():
            self.display_text("WiFi connection failed!", (50, self.height - 80), thickness=2)
            raise Exception("Failed to connect to WiFi")

        self.clear(1)
        self.display_text("Initializing Spotify Client", (35, self.height - 80), thickness=2)
        self.spotify_client = self.get_spotify_client()
        
        # Initialize volume controller with display callback
        self.volume_controller = VolumeController(
            self.spotify_client,
            display_callback=self._volume_display_callback
        )
        
        # Initialize gesture detector
        self.gesture_detector = GestureDetector(self.touch, self.width, self.height)
        self.setup_gesture_callbacks()
        
        self.clear(1)
        self.presto.update()

        # JPEG decoder with error handling
        self.j = jpegdec.JPEG(self.display)
        
        # Image cache for album covers
        self._image_cache = {}
        self._cache_size_limit = 3  # Keep last 3 album covers
        
        self.state = State()
        self.setup_buttons()
        
        print("Spotify app initialized successfully")
    
    def display_text(self, text, position, color=65535, scale=1, thickness=None):
        if thickness:
            self.display.set_thickness(2)
        x,y = position
        self.display.set_pen(color)
        self.display.text(text, x, y, scale=scale)
        self.presto.update()

    def get_spotify_client(self):
        if not hasattr(secrets, 'SPOTIFY_CREDENTIALS') or not secrets.SPOTIFY_CREDENTIALS:
            while True:
                self.clear(1)
                self.display.set_pen(self.colors.WHITE)
                self.display.text("Spotify credentials not found", 40, self.height - 80, scale=.9)
                self.presto.update()
                time.sleep(2)

        session = Session(secrets.SPOTIFY_CREDENTIALS)
        return SpotifyWebApiClient(session)
    
    def _volume_display_callback(self, volume, hide_overlay=False):
        """Callback for volume controller to update display"""
        self.state.volume = volume
        self.state.show_volume_overlay = not hide_overlay
        self.state.invalidate_hash()
        
        # Update display immediately for responsiveness
        self.display_manager.draw_volume_overlay(volume, not hide_overlay)
    
    def setup_gesture_callbacks(self):
        """Setup gesture detection callbacks"""
        # Volume gestures
        self.gesture_detector.register_gesture_callback(
            GestureDetector.GESTURE_VOLUME_UP,
            self._handle_volume_up_gesture
        )
        self.gesture_detector.register_gesture_callback(
            GestureDetector.GESTURE_VOLUME_DOWN,
            self._handle_volume_down_gesture
        )
        
        # Swipe gestures for track control
        self.gesture_detector.register_gesture_callback(
            GestureDetector.GESTURE_SWIPE_LEFT,
            self._handle_next_track_gesture
        )
        self.gesture_detector.register_gesture_callback(
            GestureDetector.GESTURE_SWIPE_RIGHT,
            self._handle_previous_track_gesture
        )
        
        # Tap gesture for play/pause
        self.gesture_detector.register_gesture_callback(
            GestureDetector.GESTURE_TAP,
            self._handle_tap_gesture
        )
    
    async def _handle_volume_up_gesture(self, position, volume_delta=5):
        """Handle volume up gesture"""
        await self.volume_controller.adjust_volume(abs(volume_delta))
    
    async def _handle_volume_down_gesture(self, position, volume_delta=-5):
        """Handle volume down gesture"""
        await self.volume_controller.adjust_volume(-abs(volume_delta))
    
    async def _handle_next_track_gesture(self, position):
        """Handle swipe left for next track"""
        if self.button_debouncer.is_debounced("next_gesture"):
            await self.spotify_client.next_async()
            self.state.latest_fetch = None  # Force refresh
    
    async def _handle_previous_track_gesture(self, position):
        """Handle swipe right for previous track"""
        if self.button_debouncer.is_debounced("prev_gesture"):
            await self.spotify_client.previous_async()
            self.state.latest_fetch = None  # Force refresh
    
    async def _handle_tap_gesture(self, position):
        """Handle tap for play/pause or show controls"""
        if self.gesture_detector.is_in_volume_zone(position[0], position[1]):
            return  # Ignore taps in volume zone
        
        if not self.state.show_controls:
            self.state.show_controls = True
            self.state.invalidate_hash()
        elif self.button_debouncer.is_debounced("play_pause_gesture"):
            await self._toggle_playback()
    
    async def _toggle_playback(self):
        """Toggle play/pause state"""
        if self.state.is_playing:
            await self.spotify_client.pause_async()
        else:
            await self.spotify_client.play_async()
        self.state.is_playing = not self.state.is_playing
        self.state.invalidate_hash()
        
    def setup_buttons(self):
        """Initializes control buttons and their behavior."""
        # --- Shared update functions ---
        def update_show_controls(state, button):
            button.enabled = state.show_controls

        def update_always_enabled(state, button):
            button.enabled = True

        def update_play_pause(state, button):
            button.enabled = state.show_controls
            button.icon = "pause.png" if state.is_playing else "play.png"

        def update_shuffle(state, button):
            button.enabled = state.show_controls
            button.icon = "shuffle_on.png" if state.shuffle else "shuffle_off.png"

        def update_repeat(state, button):
            button.enabled = state.show_controls
            button.icon = "repeat_on.png" if state.repeat else "repeat_off.png"

        def update_light(state, button):
            button.enabled = state.show_controls
            button.icon = "light_on.png" if state.toggle_leds else "light_off.png"

        # --- Async on-press handlers ---
        async def exit_app(self):
            self.state.exit = True

        async def toggle_controls(self):
            self.state.show_controls = not self.state.show_controls
            self.state.invalidate_hash()

        async def play_pause(self):
            if self.button_debouncer.is_debounced("play_pause"):
                await self._toggle_playback()

        async def next_track(self):
            if self.button_debouncer.is_debounced("next"):
                await self.spotify_client.next_async()
                self.state.latest_fetch = None
                self.state.invalidate_hash()

        async def previous_track(self):
            if self.button_debouncer.is_debounced("previous"):
                await self.spotify_client.previous_async()
                self.state.latest_fetch = None
                self.state.invalidate_hash()

        async def toggle_shuffle(self):
            if self.button_debouncer.is_debounced("shuffle"):
                new_shuffle = not self.state.shuffle
                async def shuffle_request():
                    self.spotify_client.toggle_shuffle(new_shuffle)
                await self.spotify_client._queue_request(shuffle_request)
                self.state.shuffle = new_shuffle
                self.state.invalidate_hash()

        async def toggle_repeat(self):
            if self.button_debouncer.is_debounced("repeat"):
                new_repeat = not self.state.repeat
                async def repeat_request():
                    self.spotify_client.toggle_repeat(new_repeat)
                await self.spotify_client._queue_request(repeat_request)
                self.state.repeat = new_repeat
                self.state.invalidate_hash()

        async def toggle_lights(self):
            if self.button_debouncer.is_debounced("lights"):
                self.toggle_leds(not self.state.toggle_leds)
                self.state.toggle_leds = not self.state.toggle_leds
                self.state.invalidate_hash()

        async def volume_up(self):
            if self.button_debouncer.is_debounced("volume_up"):
                await self.volume_controller.volume_up()

        async def volume_down(self):
            if self.button_debouncer.is_debounced("volume_down"):
                await self.volume_controller.volume_down()

        # --- Button configurations with volume controls ---
        buttons_config = [
            ("Exit", ["exit.png"], (0, 0, 80, 80), exit_app, update_show_controls),
            ("Next", ["next.png"], (self.center_x + 60, self.height - 100, 80, 100), next_track, update_show_controls),
            ("Previous", ["previous.png"], (self.center_x - 140, self.height - 100, 80, 100), previous_track, update_show_controls),
            ("Play", ["play.png", "pause.png"], (self.center_x - 50, self.height - 100, 80, 100), play_pause, update_play_pause),
            ("Toggle Shuffle", ["shuffle_on.png", "shuffle_off.png"], (self.center_x - 230, self.height - 100, 80, 100), toggle_shuffle, update_shuffle),
            ("Toggle Repeat", ["repeat_on.png", "repeat_off.png"], (self.center_x + 150, self.height - 100, 80, 100), toggle_repeat, update_repeat),
            ("Toggle Light", ["light_on.png", "light_off.png"], (self.width - 100, 0, 100, 80), toggle_lights, update_light),
            ("Volume Up", None, (self.width - 80, 80, 80, 40), volume_up, update_show_controls),
            ("Volume Down", None, (self.width - 80, 120, 80, 40), volume_down, update_show_controls),
            ("Toggle Controls", None, (0, 0, self.width, self.height), toggle_controls, update_always_enabled),
        ]

        # --- Create ControlButton instances ---
        self.buttons = [
            ControlButton(self.display, name, icons, bounds, on_press, update)
            for name, icons, bounds, on_press, update in buttons_config
        ]

    def run(self):
        """Starts the app's event loops."""
        loop = asyncio.get_event_loop()
        
        # Start all async components
        loop.create_task(self.initialize_async_components())
        loop.create_task(self.touch_handler_loop())
        loop.create_task(self.gesture_detector.process_touch_events())
        loop.create_task(self.display_loop())
        
        try:
            loop.run_forever()
        except KeyboardInterrupt:
            print("Shutting down...")
        finally:
            self.cleanup()
    
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            # Start memory management
            memory_manager.register_cleanup_callback(self._cleanup_memory)
            await memory_manager.start_monitoring()
            
            # Start Spotify client request processor
            await self.spotify_client.start_request_processor()
            
            # Initialize volume controller
            await self.volume_controller.initialize()
            
            # Start display manager animations
            self.display_manager.start_animation_processor()
            
            print("All async components initialized")
            memory_manager.print_memory_report()
        except Exception as e:
            print(f"Failed to initialize async components: {e}")
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            memory_manager.stop_monitoring()
            self.spotify_client._processing_requests = False
            self._cleanup_memory()
        except:
            pass
    
    def _cleanup_memory(self):
        """Memory cleanup callback"""
        try:
            # Clear image cache if memory is low
            if memory_manager.is_memory_low():
                cache_size = len(self._image_cache)
                if cache_size > 1:
                    # Keep only the most recent image
                    keys = list(self._image_cache.keys())
                    for key in keys[:-1]:
                        del self._image_cache[key]
                    print(f"Cleared {cache_size - 1} cached images")
            
            # Clear any other temporary data
            gc.collect()
        except Exception as e:
            print(f"Memory cleanup error: {e}")

    async def touch_handler_loop(self):
        """Handles touch input events and button presses with debouncing."""
        while not self.state.exit:
            try:
                self.touch.poll()

                for button in self.buttons:
                    button.update(self.state, button)
                    if button.is_pressed(self.state):
                        print(f"{button.name} pressed")
                        
                        # Show button press animation
                        self.display_manager.draw_button_animation(button.button.bounds, pressed=True)
                        
                        try:
                            # Execute async button handler
                            await button.on_press(self)
                        except Exception as e:
                            print(f"Failed to execute on_press: {e}")
                        
                        # Show button release animation
                        await asyncio.sleep_ms(100)  # Visual feedback duration
                        self.display_manager.draw_button_animation(button.button.bounds, pressed=False)
                        break
                
                # Wait here until the user stops touching the screen
                while self.touch.state:
                    self.touch.poll()
                    await asyncio.sleep_ms(10)  # Prevent blocking

                await asyncio.sleep_ms(10)  # Reduced from 1ms for better performance
                
            except Exception as e:
                print(f"Touch handler error: {e}")
                await asyncio.sleep_ms(50)

    async def show_image_async(self, img, track_id=None):
        """Displays an album cover image on the screen with caching."""
        try:
            # Check cache first
            if track_id and track_id in self._image_cache:
                cached_img = self._image_cache[track_id]
                self.j.open_RAM(memoryview(cached_img))
            elif img:
                self.j.open_RAM(memoryview(img))
                # Cache the image
                if track_id and len(self._image_cache) < self._cache_size_limit:
                    self._image_cache[track_id] = img
                elif track_id and len(self._image_cache) >= self._cache_size_limit:
                    # Remove oldest cache entry
                    oldest_key = next(iter(self._image_cache))
                    del self._image_cache[oldest_key]
                    self._image_cache[track_id] = img
            else:
                return

            img_width, img_height = self.j.get_width(), self.j.get_height()
            img_x, img_y = (self.width - img_width) // 2, (self.height - img_height) // 2

            self.display_manager.clear_layer(0)
            self.j.decode(img_x, img_y, jpegdec.JPEG_SCALE_FULL, dither=True)
            self.display_manager.mark_dirty(img_x, img_y, img_width, img_height)

        except OSError as e:
            print(f"Failed to load image: {e}")
        except Exception as e:
            print(f"Image display error: {e}")

    def _get_adaptive_interval(self):
        """Calculate adaptive polling interval based on activity and state"""
        base_interval = 15  # Base 15 seconds (reduced API calls)
        
        # Faster polling when playing music
        if self.state.is_playing:
            return 8
        
        # Slower polling when controls are hidden (inactive)
        if not self.state.show_controls:
            return 30
            
        # Standard interval for normal state
        return base_interval

    async def display_loop(self):
        """Optimized display loop with async operations and efficient updates."""
        prev_state = None
        last_volume_sync = 0
        last_api_call = 0
        consecutive_api_errors = 0
        max_api_errors = 3
        
        # Performance monitoring variables
        loop_start_time = time.time()
        frame_count = 0
        last_fps_report = time.time()
        last_memory_report = time.time()

        print("DEBUG: Starting display_loop with performance monitoring")
        print("DEBUG: [OK] _get_adaptive_interval method now available")

        while not self.state.exit:
            try:
                frame_start = time.time()
                current_time = time.time()
                frame_count += 1
                
                # DIAGNOSTIC: Calculate and report FPS every 10 seconds
                if current_time - last_fps_report >= 10:
                    fps = frame_count / (current_time - last_fps_report)
                    print(f"DEBUG: Display FPS: {fps:.2f} (target: 10+ fps)")
                    frame_count = 0
                    last_fps_report = current_time
                
                # DIAGNOSTIC: Memory report every 30 seconds
                if current_time - last_memory_report >= 30:
                    try:
                        import gc
                        gc.collect()
                        mem_free = gc.mem_free() if hasattr(gc, 'mem_free') else "unknown"
                        mem_alloc = gc.mem_alloc() if hasattr(gc, 'mem_alloc') else "unknown"
                        print(f"DEBUG: Memory - Free: {mem_free}, Allocated: {mem_alloc}")
                        print(f"DEBUG: Image cache size: {len(self._image_cache)} items")
                        last_memory_report = current_time
                    except Exception as e:
                        print(f"DEBUG: Memory report failed: {e}")
                
                # Fetch Spotify state with adaptive interval based on activity
                adaptive_interval = self._get_adaptive_interval()
                time_since_last_api = current_time - last_api_call
                
                # DIAGNOSTIC: Track API call patterns
                if not self.state.latest_fetch or current_time - self.state.latest_fetch > adaptive_interval:
                    print(f"DEBUG: API Call - Time since last: {time_since_last_api:.1f}s, Interval: {adaptive_interval}s")
                    print(f"DEBUG: API Call - Consecutive errors: {consecutive_api_errors}/{max_api_errors}")
                    last_api_call = current_time
                    self.state.latest_fetch = current_time
                    
                    # DIAGNOSTIC: Track API call success/failure patterns
                    api_start_time = time.time()
                    result = await self.fetch_state_async()
                    api_duration = time.time() - api_start_time
                    
                    if result:
                        print(f"DEBUG: [OK] API call successful in {api_duration:.2f}s")
                        consecutive_api_errors = 0  # Reset error counter
                        device_id, track, is_playing, shuffle, repeat, volume = result
                        
                        # Update state
                        self.state.track = track
                        self.state.is_playing = is_playing
                        self.state.shuffle = shuffle
                        self.state.repeat = repeat
                        
                        if volume is not None:
                            self.state.volume = volume
                            # Sync volume controller occasionally
                            if current_time - last_volume_sync > 30:  # Every 30 seconds
                                self.volume_controller._current_volume = volume
                                last_volume_sync = current_time
                        
                        if device_id:
                            self.spotify_client.session.device_id = device_id
                        
                        self.state.invalidate_hash()
                    else:
                        consecutive_api_errors += 1
                        print(f"DEBUG: ✗ API call failed in {api_duration:.2f}s (error #{consecutive_api_errors})")
                        
                        # DIAGNOSTIC: Back off API calls if too many consecutive errors
                        if consecutive_api_errors >= max_api_errors:
                            print(f"DEBUG: ⚠️  Too many API errors, backing off for 60s")
                            self.state.latest_fetch = current_time + 60  # Back off for 1 minute

                await asyncio.sleep_ms(50)  # Yield control

                # DIAGNOSTIC: Track album cover loading performance
                image_start_time = time.time()
                # Load album cover if track changed (async)
                if (not prev_state or
                    (prev_state.track or {}).get('id') != (self.state.track or {}).get("id")):
                    if self.state.track:
                        track_id = self.state.track.get('id')
                        print(f"DEBUG: Track changed to: {self.state.track.get('name', 'Unknown')}")
                        if track_id not in self._image_cache:
                            print(f"DEBUG: Loading new album cover for track {track_id}")
                            # Load image asynchronously
                            asyncio.create_task(self._load_album_cover_async(track_id))
                        else:
                            print(f"DEBUG: Using cached album cover for track {track_id}")
                            # Use cached image immediately
                            await self.show_image_async(None, track_id)
                image_duration = time.time() - image_start_time
                if image_duration > 0.1:  # Log if image processing takes > 100ms
                    print(f"DEBUG: Image processing took {image_duration:.3f}s")

                await asyncio.sleep_ms(50)  # Yield control

                # DIAGNOSTIC: Track display update performance
                display_start_time = time.time()
                state_changed = prev_state != self.state
                
                # Update display if state changed (optimized)
                if state_changed:
                    print(f"DEBUG: State changed, updating display...")
                    # Use display manager for optimized updates
                    self.display_manager.clear_layer(1)
                    
                    # Draw buttons efficiently
                    for button in self.buttons:
                        button.update(self.state, button)
                        button.draw(self.state)
                    
                    # Draw track info using display manager
                    if self.state.track:
                        self.display_manager.draw_track_info(self.state.track, self.state.show_controls)
                    
                    # Draw volume overlay if visible
                    if self.state.show_volume_overlay:
                        self.display_manager.draw_volume_overlay(self.state.volume, True)
                    
                    # Update display efficiently
                    await self.display_manager.update_display()
                    prev_state = self.state.copy()
                    
                    display_duration = time.time() - display_start_time
                    print(f"DEBUG: Display update took {display_duration:.3f}s")
                    if display_duration > 0.1:  # Log if display update takes > 100ms
                        print(f"DEBUG: ⚠️  Slow display update: {display_duration:.3f}s")

                # Memory management with diagnostic timing
                if current_time % 30 < 1:  # Every ~30 seconds
                    gc_start = time.time()
                    gc.collect()
                    gc_duration = time.time() - gc_start
                    print(f"DEBUG: Garbage collection took {gc_duration:.3f}s")
                    
                    try:
                        memory_stats = self.display_manager.get_memory_usage()
                        print(f"DEBUG: Memory stats: {memory_stats}")
                    except Exception as e:
                        print(f"DEBUG: Memory stats unavailable: {e}")

                # DIAGNOSTIC: Track loop timing
                frame_duration = time.time() - frame_start
                if frame_duration > 0.2:  # Log if frame takes > 200ms
                    print(f"DEBUG: ⚠️  Slow frame: {frame_duration:.3f}s")

                await asyncio.sleep_ms(100)  # Main loop timing
                
            except Exception as e:
                print(f"Display loop error: {e}")
                await asyncio.sleep_ms(500)  # Recovery delay
    
    async def _load_album_cover_async(self, track_id):
        """Load album cover asynchronously without blocking UI"""
        try:
            track = self.state.track
            if not track or track.get('id') != track_id:
                return  # Track changed while loading
            
            img = await self.get_album_cover_async(track)
            if img and self.state.track and self.state.track.get('id') == track_id:
                await self.show_image_async(img, track_id)
        except Exception as e:
            print(f"Async album cover loading error: {e}")
    
    async def fetch_state_async(self):
        """Async version of fetch_state with timeout and error handling"""
        try:
            current_track = None
            is_playing = False
            shuffle = False
            repeat = False
            device_id = None
            volume = None
            
            # Try to get current playing track with timeout
            resp = await self.spotify_client.current_playing_async()
            if resp and resp.get("item"):
                current_track = resp["item"]
                is_playing = resp.get("is_playing", False)
                shuffle = resp.get("shuffle_state", False)
                repeat = resp.get("repeat_state", "off") != "off"
                device_id = resp.get("device", {}).get("id")
                volume = resp.get("device", {}).get("volume_percent")
                print(f"Got current playing track: {current_track.get('name')}")
            
            # Fall back to recently played if no current track
            if not current_track:
                try:
                    print("DEBUG: Attempting recently_played request")
                    # Check for MicroPython compatibility
                    if hasattr(asyncio, 'to_thread') and hasattr(asyncio, 'wait_for'):
                        print("DEBUG: Using standard asyncio for recently_played")
                        resp = await asyncio.wait_for(
                            asyncio.create_task(asyncio.to_thread(self.spotify_client.recently_played)),
                            timeout=3.0
                        )
                    else:
                        print("DEBUG: Using MicroPython fallback for recently_played")
                        # MicroPython fallback
                        async def recently_played_wrapper():
                            await asyncio.sleep_ms(0)  # Yield control
                            return self.spotify_client.recently_played()
                        
                        resp = await recently_played_wrapper()
                    if resp and resp.get("items"):
                        current_track = resp["items"][0]["track"]
                        print(f"Got recently played track: {current_track.get('name')}")
                except asyncio.TimeoutError:
                    print("Recently played request timed out")
                except Exception as e:
                    print(f"Failed to get recently played track: {e}")
            
            if current_track:
                return device_id, current_track, is_playing, shuffle, repeat, volume
            
            return None
            
        except Exception as e:
            print(f"Async fetch state error: {e}")
            return None
    
    async def get_album_cover_async(self, track):
        """Async version of get_album_cover with caching and error handling"""
        try:
            if not track or "album" not in track:
                return None
            
            images = track["album"].get("images", [])
            if len(images) < 2:
                return None
            
            img_url = images[1]["url"]
            resize_url = f"https://wsrv.nl/?url={img_url}&w=480&h=480"
            
            # Async HTTP request with timeout
            print("DEBUG: Attempting album cover download")
            if hasattr(asyncio, 'to_thread') and hasattr(asyncio, 'wait_for'):
                print("DEBUG: Using standard asyncio for image download")
                response = await asyncio.wait_for(
                    asyncio.create_task(asyncio.to_thread(requests.get, resize_url)),
                    timeout=10.0
                )
            else:
                print("DEBUG: Using MicroPython fallback for image download")
                # MicroPython fallback
                async def get_image_wrapper():
                    await asyncio.sleep_ms(0)  # Yield control
                    return requests.get(resize_url)
                
                response = await get_image_wrapper()
            
            if response.status_code == 200:
                img = response.content
                response.close()
                return img
            else:
                print(f"Failed to fetch image: {response.status_code}")
                response.close()
                return None
                
        except asyncio.TimeoutError:
            print("Album cover request timed out")
            return None
        except Exception as e:
            print(f"Async album cover error: {e}")
            return None

def fetch_state(spotify_client):
    """Fetches the current playback state from Spotify."""

    current_track = None
    is_playing = False
    shuffle = False
    repeat = False
    device_id = None
    try:
        resp = spotify_client.current_playing()
        if resp and resp.get("item"):
            current_track = resp["item"]
            is_playing = resp.get("is_playing")
            shuffle = resp.get("shuffle_state")
            repeat = resp.get("repeat_state", "off") != "off" 
            device_id = resp["device"]["id"]
            print("Got current playing track: " + current_track.get("name"))
    except Exception as e:
        print("Failed to get current playing track:", e)

    if not current_track:
        try:
            resp = spotify_client.recently_played()
            if resp and resp.get("items"):
                current_track = resp["items"][0]["track"]
                print("Got recently playing track: " + current_track.get("name"))
        except Exception as e:
            print("Failed to get recently played track:", e)

    if not current_track:
        return None

    return device_id, current_track, is_playing, shuffle, repeat

def get_album_cover(track):
    """Fetches and resizes the album cover image for the given track."""

    img_url = track["album"]["images"][1]["url"]
    
    img = None
    resize_url = f"https://wsrv.nl/?url={img_url}&w=480&h=480"
    try:
        response = requests.get(resize_url)
        if response.status_code == 200:
            img = response.content
        else:
            print("Failed to fetch image:", response.status_code)
    except Exception as e:
        print("Fetch image error:", e)
        
    return img

def launch():
    """Launches the Spotify app and starts the event loop."""
    app = Spotify()
    app.run()

    app.clear()
    del app
    gc.collect()