"""
RP2350 Compatibility Test Suite for PrestoDeck Spotify Application
Tests asyncio features, memory management, and hardware interfaces.
"""

import sys
import gc
import time
import uasyncio as asyncio
from micropython import const

# Test configuration
TEST_TIMEOUT = const(30)  # 30 seconds timeout for tests
MEMORY_TEST_SIZE = const(100)  # 100KB test objects

class RP2350CompatibilityTester:
    """Comprehensive compatibility tester for RP2350 platform"""
    
    def __init__(self):
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        
    def log_result(self, test_name, passed, details=""):
        """Log test result"""
        self.test_results[test_name] = {
            'passed': passed,
            'details': details
        }
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            print(f"✅ {test_name}: PASSED {details}")
        else:
            print(f"❌ {test_name}: FAILED {details}")
    
    def test_platform_info(self):
        """Test platform identification"""
        try:
            print(f"Platform: {sys.platform}")
            print(f"MicroPython version: {sys.version}")
            print(f"Implementation: {sys.implementation}")
            
            # Check if this is RP2350
            is_rp2350 = 'rp2' in sys.platform.lower()
            self.log_result("Platform Detection", is_rp2350, 
                          f"Detected: {sys.platform}")
            return is_rp2350
        except Exception as e:
            self.log_result("Platform Detection", False, str(e))
            return False
    
    def test_memory_info(self):
        """Test memory information availability"""
        try:
            gc.collect()
            
            # Test basic memory functions
            has_mem_free = hasattr(gc, 'mem_free')
            has_mem_alloc = hasattr(gc, 'mem_alloc')
            
            if has_mem_free and has_mem_alloc:
                free_mem = gc.mem_free()
                alloc_mem = gc.mem_alloc()
                total_mem = free_mem + alloc_mem
                
                # RP2350 should have ~520KB SRAM
                expected_min = 400 * 1024  # At least 400KB
                expected_max = 600 * 1024  # At most 600KB
                
                memory_ok = expected_min <= total_mem <= expected_max
                details = f"Total: {total_mem//1024}KB, Free: {free_mem//1024}KB"
                
                self.log_result("Memory Info", memory_ok, details)
                return memory_ok
            else:
                self.log_result("Memory Info", False, "mem_free/mem_alloc not available")
                return False
                
        except Exception as e:
            self.log_result("Memory Info", False, str(e))
            return False
    
    def test_asyncio_features(self):
        """Test asyncio feature availability"""
        features_to_test = [
            'create_task',
            'sleep',
            'sleep_ms',
            'Event',
            'Lock',
            'Queue',
            'wait_for',
            'gather',
            'to_thread'
        ]
        
        results = {}
        for feature in features_to_test:
            try:
                has_feature = hasattr(asyncio, feature)
                results[feature] = has_feature
                
                # Special test for Queue
                if feature == 'Queue' and has_feature:
                    try:
                        q = asyncio.Queue(maxsize=5)
                        results[feature] = True
                    except Exception:
                        results[feature] = False
                        
            except Exception:
                results[feature] = False
        
        # Critical features for Spotify app
        critical_features = ['create_task', 'sleep', 'sleep_ms', 'Event', 'Lock']
        critical_ok = all(results.get(f, False) for f in critical_features)
        
        # Nice-to-have features
        optional_features = ['Queue', 'wait_for', 'gather', 'to_thread']
        optional_count = sum(1 for f in optional_features if results.get(f, False))
        
        details = f"Critical: {critical_ok}, Optional: {optional_count}/{len(optional_features)}"
        self.log_result("Asyncio Features", critical_ok, details)
        
        # Log individual feature results
        for feature, available in results.items():
            status = "✅" if available else "❌"
            print(f"  {status} asyncio.{feature}")
        
        return critical_ok, results
    
    async def test_asyncio_functionality(self):
        """Test actual asyncio functionality"""
        try:
            # Test basic async operations
            start_time = time.ticks_ms()
            await asyncio.sleep_ms(100)
            elapsed = time.ticks_diff(time.ticks_ms(), start_time)
            
            timing_ok = 90 <= elapsed <= 150  # Allow some variance
            
            # Test Event
            event = asyncio.Event()
            event.set()
            event_ok = event.is_set()
            
            # Test Lock
            lock = asyncio.Lock()
            async with lock:
                lock_ok = True
            
            # Test task creation
            async def dummy_task():
                await asyncio.sleep_ms(10)
                return "success"
            
            task = asyncio.create_task(dummy_task())
            result = await task
            task_ok = result == "success"
            
            overall_ok = timing_ok and event_ok and lock_ok and task_ok
            details = f"Timing: {timing_ok}, Event: {event_ok}, Lock: {lock_ok}, Task: {task_ok}"
            
            self.log_result("Asyncio Functionality", overall_ok, details)
            return overall_ok
            
        except Exception as e:
            self.log_result("Asyncio Functionality", False, str(e))
            return False
    
    async def test_memory_pressure(self):
        """Test memory behavior under pressure"""
        try:
            initial_free = gc.mem_free()
            objects = []
            
            # Allocate memory in chunks
            for i in range(5):
                obj = bytearray(MEMORY_TEST_SIZE * 1024)  # 100KB objects
                objects.append(obj)
                gc.collect()
                
                current_free = gc.mem_free()
                print(f"  After {i+1} objects: {current_free//1024}KB free")
                
                # Yield control
                await asyncio.sleep_ms(10)
            
            # Test cleanup
            objects.clear()
            gc.collect()
            final_free = gc.mem_free()
            
            # Should recover most memory
            recovery_ratio = final_free / initial_free
            memory_ok = recovery_ratio > 0.8  # Should recover at least 80%
            
            details = f"Recovery: {recovery_ratio:.2f}, Final: {final_free//1024}KB"
            self.log_result("Memory Pressure", memory_ok, details)
            return memory_ok
            
        except Exception as e:
            self.log_result("Memory Pressure", False, str(e))
            return False
    
    def test_hardware_interfaces(self):
        """Test hardware interface availability"""
        interfaces_to_test = [
            'jpegdec',
            'pngdec', 
            'urequests',
            'usocket',
            'ujson'
        ]
        
        results = {}
        for interface in interfaces_to_test:
            try:
                __import__(interface)
                results[interface] = True
            except ImportError:
                results[interface] = False
        
        all_available = all(results.values())
        available_count = sum(results.values())
        
        details = f"{available_count}/{len(interfaces_to_test)} available"
        self.log_result("Hardware Interfaces", all_available, details)
        
        # Log individual results
        for interface, available in results.items():
            status = "✅" if available else "❌"
            print(f"  {status} {interface}")
        
        return all_available
    
    async def run_all_tests(self):
        """Run complete test suite"""
        print("🔧 RP2350 Compatibility Test Suite")
        print("=" * 50)
        
        # Platform tests
        print("\n📋 Platform Information:")
        self.test_platform_info()
        
        print("\n💾 Memory Tests:")
        self.test_memory_info()
        await self.test_memory_pressure()
        
        print("\n⚡ Asyncio Tests:")
        critical_ok, features = self.test_asyncio_features()
        await self.test_asyncio_functionality()
        
        print("\n🔌 Hardware Interface Tests:")
        self.test_hardware_interfaces()
        
        # Summary
        print("\n" + "=" * 50)
        print(f"📊 TEST SUMMARY")
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        # Compatibility assessment
        if self.passed_tests == self.total_tests:
            print("\n🟢 EXCELLENT: Full RP2350 compatibility")
        elif self.passed_tests >= self.total_tests * 0.8:
            print("\n🟡 GOOD: Minor compatibility issues")
        else:
            print("\n🔴 POOR: Significant compatibility issues")
        
        return self.test_results

# Test execution
async def main():
    """Main test execution"""
    tester = RP2350CompatibilityTester()
    results = await tester.run_all_tests()
    
    # Save results for analysis
    print(f"\n💾 Test results saved to memory")
    return results

# Run tests if executed directly
if __name__ == "__main__":
    try:
        results = asyncio.run(main())
        print("\n✅ All tests completed successfully")
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
