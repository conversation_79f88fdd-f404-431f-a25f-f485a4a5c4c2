"""
PrestoDeck Spotify App Deployment Test Suite
Comprehensive testing for RP2350 deployment readiness.
"""

import gc
import time
import uasyncio as asyncio
from micropython import const

class DeploymentTester:
    """Comprehensive deployment testing for Spotify app"""
    
    def __init__(self):
        self.test_results = {}
        self.critical_failures = []
        
    def log_test(self, name, passed, details="", critical=False):
        """Log test result"""
        self.test_results[name] = {
            'passed': passed,
            'details': details,
            'critical': critical
        }
        
        if critical and not passed:
            self.critical_failures.append(name)
        
        status = "✅" if passed else "❌"
        priority = " [CRITICAL]" if critical else ""
        print(f"{status} {name}{priority}: {details}")
    
    def test_secrets_configuration(self):
        """Test secrets.py configuration"""
        try:
            import secrets
            
            # Check WiFi credentials
            has_wifi_ssid = hasattr(secrets, 'WIFI_SSID') and secrets.WIFI_SSID != "WIFI_SSID"
            has_wifi_password = hasattr(secrets, 'WIFI_PASSWORD') and secrets.WIFI_PASSWORD != "WIFI_PASSWORD"
            
            # Check Spotify credentials
            has_spotify_creds = hasattr(secrets, 'SPOTIFY_CREDENTIALS') and secrets.SPOTIFY_CREDENTIALS
            
            wifi_ok = has_wifi_ssid and has_wifi_password
            spotify_ok = has_spotify_creds
            
            self.log_test("WiFi Configuration", wifi_ok, 
                         f"SSID: {has_wifi_ssid}, Password: {has_wifi_password}", critical=True)
            self.log_test("Spotify Configuration", spotify_ok,
                         f"Credentials: {has_spotify_creds}", critical=True)
            
            return wifi_ok and spotify_ok
            
        except ImportError:
            self.log_test("Secrets Import", False, "secrets.py not found", critical=True)
            return False
        except Exception as e:
            self.log_test("Secrets Configuration", False, str(e), critical=True)
            return False
    
    def test_module_imports(self):
        """Test all required module imports"""
        modules_to_test = [
            # Core modules
            ('gc', True),
            ('time', True),
            ('uasyncio', True),
            
            # Hardware modules
            ('jpegdec', True),
            ('pngdec', True),
            ('urequests', True),
            ('usocket', False),
            ('ujson', False),
            
            # Application modules
            ('base', True),
            ('touch', True),
        ]
        
        import_results = {}
        for module, critical in modules_to_test:
            try:
                __import__(module)
                import_results[module] = True
                self.log_test(f"Import {module}", True, "Available", critical=critical)
            except ImportError:
                import_results[module] = False
                self.log_test(f"Import {module}", False, "Not available", critical=critical)
        
        # Test Spotify app modules
        spotify_modules = [
            'applications.spotify.spotify_client',
            'applications.spotify.volume_controller', 
            'applications.spotify.gesture_detector',
            'applications.spotify.display_manager',
            'applications.spotify.memory_manager'
        ]
        
        for module in spotify_modules:
            try:
                __import__(module)
                import_results[module] = True
                self.log_test(f"Import {module.split('.')[-1]}", True, "Available", critical=True)
            except ImportError as e:
                import_results[module] = False
                self.log_test(f"Import {module.split('.')[-1]}", False, str(e), critical=True)
        
        critical_modules = [m for m, c in modules_to_test if c] + spotify_modules
        critical_ok = all(import_results.get(m, False) for m in critical_modules)
        
        return critical_ok
    
    def test_file_structure(self):
        """Test required file structure"""
        required_files = [
            'src/main.py',
            'src/base.py',
            'src/secrets.py',
            'src/applications/spotify/spotify.py',
            'src/applications/spotify/icon.png',
            'src/applications/spotify/icons/play.png',
            'src/applications/spotify/icons/pause.png',
            'src/applications/spotify/icons/next.png',
            'src/applications/spotify/icons/previous.png'
        ]
        
        missing_files = []
        for file_path in required_files:
            try:
                # Try to open file
                with open(file_path, 'rb') as f:
                    f.read(1)  # Just check if readable
            except OSError:
                missing_files.append(file_path)
        
        files_ok = len(missing_files) == 0
        details = f"Missing: {len(missing_files)} files" if missing_files else "All files present"
        
        self.log_test("File Structure", files_ok, details, critical=True)
        
        if missing_files:
            for file in missing_files[:5]:  # Show first 5 missing files
                print(f"  ❌ Missing: {file}")
        
        return files_ok
    
    async def test_memory_capacity(self):
        """Test memory capacity for Spotify app"""
        try:
            initial_free = gc.mem_free()
            
            # Simulate Spotify app memory usage
            simulated_objects = []
            
            # Simulate app overhead (~150KB)
            app_overhead = bytearray(150 * 1024)
            simulated_objects.append(app_overhead)
            
            # Simulate album covers (100KB each)
            for i in range(4):  # Test with 4 album covers
                album_cover = bytearray(100 * 1024)
                simulated_objects.append(album_cover)
                gc.collect()
                
                current_free = gc.mem_free()
                print(f"  After {i+1} album covers: {current_free//1024}KB free")
                
                # Should still have reasonable memory available
                if current_free < 50 * 1024:  # Less than 50KB free
                    break
                
                await asyncio.sleep_ms(10)
            
            # Test cleanup
            simulated_objects.clear()
            gc.collect()
            final_free = gc.mem_free()
            
            # Calculate memory usage
            peak_usage = initial_free - min(gc.mem_free() for _ in range(1))
            recovery_ratio = final_free / initial_free
            
            # Memory test passes if we can handle at least 3 album covers
            memory_ok = len(simulated_objects) >= 4 and recovery_ratio > 0.8
            
            details = f"Peak usage: {peak_usage//1024}KB, Recovery: {recovery_ratio:.2f}"
            self.log_test("Memory Capacity", memory_ok, details, critical=True)
            
            return memory_ok
            
        except Exception as e:
            self.log_test("Memory Capacity", False, str(e), critical=True)
            return False
    
    async def test_performance_baseline(self):
        """Test basic performance characteristics"""
        try:
            # Test async task switching performance
            switch_count = 0
            start_time = time.ticks_ms()
            
            async def counter_task():
                nonlocal switch_count
                for _ in range(100):
                    switch_count += 1
                    await asyncio.sleep_ms(1)
            
            # Run multiple tasks concurrently
            await asyncio.gather(
                counter_task(),
                counter_task(),
                counter_task()
            )
            
            elapsed = time.ticks_diff(time.ticks_ms(), start_time)
            
            # Should complete in reasonable time
            performance_ok = elapsed < 1000 and switch_count == 300  # Less than 1 second
            
            details = f"300 task switches in {elapsed}ms"
            self.log_test("Performance Baseline", performance_ok, details)
            
            return performance_ok
            
        except Exception as e:
            self.log_test("Performance Baseline", False, str(e))
            return False
    
    def test_hardware_compatibility(self):
        """Test hardware-specific compatibility"""
        try:
            # Test display modules
            import jpegdec
            import pngdec
            
            # Test network modules  
            import urequests
            
            # Test touch module
            from touch import Button
            
            # Basic functionality test
            # Note: Can't actually test hardware without device
            hardware_ok = True
            
            self.log_test("Hardware Compatibility", hardware_ok, 
                         "All hardware modules available")
            
            return hardware_ok
            
        except Exception as e:
            self.log_test("Hardware Compatibility", False, str(e), critical=True)
            return False
    
    async def test_app_initialization(self):
        """Test Spotify app initialization (without full startup)"""
        try:
            # Test State class
            from applications.spotify.spotify import State
            state = State()
            state_ok = hasattr(state, 'is_playing') and hasattr(state, 'track')
            
            # Test ControlButton class
            from applications.spotify.spotify import ControlButton
            # Can't fully test without display, but check class exists
            button_ok = ControlButton is not None
            
            # Test memory manager
            from applications.spotify.memory_manager import memory_manager
            memory_mgr_ok = hasattr(memory_manager, 'get_memory_info')
            
            init_ok = state_ok and button_ok and memory_mgr_ok
            
            details = f"State: {state_ok}, Button: {button_ok}, MemMgr: {memory_mgr_ok}"
            self.log_test("App Initialization", init_ok, details, critical=True)
            
            return init_ok
            
        except Exception as e:
            self.log_test("App Initialization", False, str(e), critical=True)
            return False
    
    async def run_deployment_tests(self):
        """Run complete deployment test suite"""
        print("🚀 PrestoDeck Spotify Deployment Test Suite")
        print("=" * 60)
        
        # Configuration tests
        print("\n📋 Configuration Tests:")
        config_ok = self.test_secrets_configuration()
        
        # Module and file tests
        print("\n📦 Module and File Tests:")
        imports_ok = self.test_module_imports()
        files_ok = self.test_file_structure()
        
        # Hardware tests
        print("\n🔌 Hardware Tests:")
        hardware_ok = self.test_hardware_compatibility()
        
        # Memory and performance tests
        print("\n💾 Memory and Performance Tests:")
        memory_ok = await self.test_memory_capacity()
        perf_ok = await self.test_performance_baseline()
        
        # Application tests
        print("\n🎵 Application Tests:")
        app_ok = await self.test_app_initialization()
        
        # Summary
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r['passed'])
        critical_passed = len(self.critical_failures) == 0
        
        print("\n" + "=" * 60)
        print(f"🚀 DEPLOYMENT TEST SUMMARY")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Critical Failures: {len(self.critical_failures)}")
        
        # Deployment readiness assessment
        if critical_passed and passed_tests == total_tests:
            print("\n🟢 READY: Spotify app is ready for RP2350 deployment")
            deployment_status = "READY"
        elif critical_passed and passed_tests >= total_tests * 0.8:
            print("\n🟡 MOSTLY READY: Minor issues, but should work")
            deployment_status = "MOSTLY_READY"
        elif not critical_passed:
            print("\n🔴 NOT READY: Critical issues must be fixed")
            print("Critical failures:")
            for failure in self.critical_failures:
                print(f"  ❌ {failure}")
            deployment_status = "NOT_READY"
        else:
            print("\n🟡 ISSUES: Significant problems detected")
            deployment_status = "ISSUES"
        
        return {
            'status': deployment_status,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'critical_failures': self.critical_failures,
            'results': self.test_results
        }

# Main execution
async def main():
    """Run deployment tests"""
    tester = DeploymentTester()
    return await tester.run_deployment_tests()

if __name__ == "__main__":
    try:
        results = asyncio.run(main())
        print(f"\n✅ Deployment testing completed: {results['status']}")
    except Exception as e:
        print(f"\n❌ Deployment testing failed: {e}")
